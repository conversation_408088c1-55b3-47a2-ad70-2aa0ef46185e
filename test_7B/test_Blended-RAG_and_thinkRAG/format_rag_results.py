#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修改RAG测试结果文件格式
使其与现有的测试结果格式保持一致
"""

import os
import json
import shutil
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

class RAGResultFormatter:
    """RAG结果格式化器"""
    
    def __init__(self, results_dir: str, processed_data_path: str, reference_file_path: str):
        """
        初始化格式化器
        
        Args:
            results_dir: RAG测试结果目录
            processed_data_path: processed_data.json文件路径
            reference_file_path: 参考格式文件路径
        """
        self.results_dir = Path(results_dir)
        self.processed_data_path = Path(processed_data_path)
        self.reference_file_path = Path(reference_file_path)
        
        # 验证路径存在
        if not self.results_dir.exists():
            raise FileNotFoundError(f"结果目录不存在: {results_dir}")
        if not self.processed_data_path.exists():
            raise FileNotFoundError(f"数据源文件不存在: {processed_data_path}")
        if not self.reference_file_path.exists():
            raise FileNotFoundError(f"参考文件不存在: {reference_file_path}")
        
        # 加载processed_data.json
        self.processed_data = self.load_processed_data()
        print(f"✓ 加载了 {len(self.processed_data)} 条processed_data记录")
        
        # 加载参考文件结构
        self.reference_structure = self.load_reference_structure()
        print(f"✓ 加载参考文件结构完成")
    
    def load_processed_data(self) -> Dict[str, Dict[str, Any]]:
        """加载processed_data.json并建立课例ID索引"""
        with open(self.processed_data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 建立课例ID到数据的映射
        indexed_data = {}
        for item in data:
            lesson_id = item.get('item', {}).get('课例id')
            if lesson_id:
                indexed_data[lesson_id] = item
        
        return indexed_data
    
    def load_reference_structure(self) -> Dict[str, Any]:
        """加载参考文件结构"""
        with open(self.reference_file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def get_expected_result(self, lesson_id: str) -> Optional[str]:
        """根据课例ID获取expected_result"""
        if lesson_id in self.processed_data:
            return self.processed_data[lesson_id].get('result')
        return None
    
    def format_single_result(self, result_item: Dict[str, Any]) -> Dict[str, Any]:
        """格式化单个测试结果"""
        # 获取课例ID
        lesson_id = result_item.get('item', {}).get('课例id', '')
        
        # 获取expected_result
        expected_result = self.get_expected_result(lesson_id)
        
        # 构建格式化后的结果
        formatted_result = {
            "lesson_id": lesson_id,
            "model_name": result_item.get('model_name', ''),
            "course_name": result_item.get('item', {}).get('课程名称', ''),
            "prompt": result_item.get('prompt', ''),
            "response": result_item.get('raw_response', ''),  # raw_response -> response
            "expected_result": expected_result,
            "timestamp": result_item.get('timestamp', ''),
            "inference_time": 0.0,  # 默认值，因为原数据中没有
            "status": "success" if result_item.get('raw_response') else "failed"
        }
        
        return formatted_result
    
    def create_formatted_structure(self, results: List[Dict[str, Any]], 
                                 rag_type: str, models: List[str]) -> Dict[str, Any]:
        """创建格式化的JSON结构"""
        # 统计信息
        total_tests = len(results)
        successful_tests = len([r for r in results if r.get('status') == 'success'])
        failed_tests = total_tests - successful_tests
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        
        # 按模型统计
        model_stats = {}
        for model in models:
            model_results = [r for r in results if r.get('model_name') == model]
            model_stats[model] = {
                "total": len(model_results),
                "success": len([r for r in model_results if r.get('status') == 'success']),
                "failed": len([r for r in model_results if r.get('status') == 'failed']),
                "avg_inference_time": 0.0,  # 默认值
                "total_inference_time": 0.0  # 默认值
            }
        
        # 构建完整结构
        formatted_structure = {
            "test_configuration": {
                "test_mode": "limit",
                "rag_type": rag_type,
                "gpus_used": [0, 1, 2, 3],  # 默认值
                "total_models": len(models),
                "total_tests": total_tests,
                "test_timestamp": datetime.now().isoformat()
            },
            "overall_statistics": {
                "successful_tests": successful_tests,
                "failed_tests": failed_tests,
                "success_rate": success_rate
            },
            "model_statistics": model_stats,
            "detailed_results": results
        }
        
        return formatted_structure
    
    def process_json_file(self, file_path: Path) -> bool:
        """处理单个JSON文件"""
        try:
            print(f"处理文件: {file_path.name}")
            
            # 读取原始文件
            with open(file_path, 'r', encoding='utf-8') as f:
                original_data = json.load(f)
            
            # 判断是否需要处理（检查是否已经是新格式）
            if isinstance(original_data, dict) and 'detailed_results' in original_data:
                print(f"  ⚠️  文件已经是新格式，跳过: {file_path.name}")
                return True
            
            # 确保原始数据是列表格式
            if not isinstance(original_data, list):
                print(f"  ❌ 文件格式不正确，跳过: {file_path.name}")
                return False
            
            # 格式化每个结果项
            formatted_results = []
            missing_expected_results = 0
            
            for item in original_data:
                formatted_item = self.format_single_result(item)
                if formatted_item['expected_result'] is None:
                    missing_expected_results += 1
                formatted_results.append(formatted_item)
            
            # 确定RAG类型和模型列表
            rag_type = "unknown"
            if "blended" in file_path.name.lower():
                rag_type = "blendedRAG"
            elif "think" in file_path.name.lower():
                rag_type = "thinkRAG"
            
            models = list(set([r['model_name'] for r in formatted_results if r['model_name']]))
            
            # 创建格式化结构
            formatted_structure = self.create_formatted_structure(formatted_results, rag_type, models)
            
            # 备份原文件
            backup_path = file_path.with_suffix('.json.backup')
            shutil.copy2(file_path, backup_path)
            
            # 保存格式化后的文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(formatted_structure, f, ensure_ascii=False, indent=2)
            
            print(f"  ✓ 处理完成: {len(formatted_results)} 条记录")
            if missing_expected_results > 0:
                print(f"  ⚠️  缺少expected_result的记录: {missing_expected_results} 条")
            print(f"  ✓ 原文件已备份为: {backup_path.name}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 处理文件失败: {file_path.name} - {e}")
            return False
    
    def process_all_files(self) -> Dict[str, int]:
        """处理所有JSON文件"""
        print("=" * 60)
        print("开始批量处理RAG测试结果文件")
        print("=" * 60)
        
        # 统计信息
        stats = {
            "total_files": 0,
            "processed_files": 0,
            "skipped_files": 0,
            "failed_files": 0
        }
        
        # 遍历所有JSON文件
        json_files = list(self.results_dir.rglob("*.json"))
        
        # 排除summary文件和backup文件
        json_files = [f for f in json_files 
                     if not f.name.startswith('rag_test_summary') 
                     and not f.name.endswith('.backup')
                     and not f.name.endswith('_summary_')]
        
        stats["total_files"] = len(json_files)
        
        for file_path in json_files:
            if self.process_json_file(file_path):
                stats["processed_files"] += 1
            else:
                stats["failed_files"] += 1
        
        return stats
    
    def print_summary(self, stats: Dict[str, int]):
        """打印处理摘要"""
        print("\n" + "=" * 60)
        print("处理完成摘要")
        print("=" * 60)
        print(f"总文件数: {stats['total_files']}")
        print(f"成功处理: {stats['processed_files']}")
        print(f"跳过文件: {stats['skipped_files']}")
        print(f"失败文件: {stats['failed_files']}")
        print(f"成功率: {stats['processed_files']/stats['total_files']*100:.1f}%" 
              if stats['total_files'] > 0 else "0%")
        print("\n✓ 所有原文件都已备份为 .backup 文件")
        print("✓ 格式化完成，文件结构已与参考文件保持一致")

def main():
    """主函数"""
    # 配置路径
    results_dir = "/home/<USER>/test_models/test_7B/test_Blended-RAG_and_thinkRAG/results"
    processed_data_path = "/home/<USER>/test_models/processed_data.json"
    reference_file_path = "/home/<USER>/test_models/test_7B/results/第一组：simple+50数据.json"
    
    try:
        # 创建格式化器
        formatter = RAGResultFormatter(results_dir, processed_data_path, reference_file_path)
        
        # 处理所有文件
        stats = formatter.process_all_files()
        
        # 打印摘要
        formatter.print_summary(stats)
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
