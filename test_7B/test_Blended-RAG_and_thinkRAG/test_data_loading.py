#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试RAG数据加载功能
"""

import os
import sys
# 添加当前目录到路径，以便导入run_rag_tests
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from run_rag_tests import RAGDataLoader

def test_data_loading():
    """测试数据加载功能"""
    print("开始测试RAG数据加载功能...")
    
    # 初始化数据加载器
    try:
        data_loader = RAGDataLoader(
            blended_rag_path="/home/<USER>/test_models/cache/Blended-RAG_retrieval_results",
            think_rag_path="/home/<USER>/test_models/cache/thinkRAG_retrieval_results",
            transcript_path="/home/<USER>/test_models/download"
        )
        print("✓ 数据加载器初始化成功")
    except Exception as e:
        print(f"✗ 数据加载器初始化失败: {e}")
        return
    
    # 测试录音文本加载
    try:
        transcripts = data_loader.load_transcript_files()
        print(f"✓ 成功加载 {len(transcripts)} 个录音文本文件")
        
        # 显示前几个示例
        count = 0
        for lesson_id, (lesson_name, content) in transcripts.items():
            if count < 3:
                print(f"  示例 {count+1}: {lesson_id} - {lesson_name} ({len(content)} 字符)")
                count += 1
            else:
                break
    except Exception as e:
        print(f"✗ 录音文本加载失败: {e}")
        return
    
    # 测试RAG提示词加载
    for rag_type in ["blended", "think"]:
        for model_name in ["glm4_9b", "qwen25_7b", "qwen3_4b"]:
            try:
                prompts = data_loader.load_rag_prompts(rag_type, model_name)
                print(f"✓ 成功加载 {len(prompts)} 个{rag_type}RAG提示词 (模型: {model_name})")
            except Exception as e:
                print(f"✗ {rag_type}RAG提示词加载失败 (模型: {model_name}): {e}")
    
    # 测试创建测试数据
    try:
        test_data = data_loader.create_test_data("blended", "glm4_9b", limit=2)
        print(f"✓ 成功创建 {len(test_data)} 条测试数据")
        
        if test_data:
            # 显示第一条测试数据的信息
            first_item = test_data[0]
            print(f"  示例数据:")
            print(f"    课例ID: {first_item['item']['课例id']}")
            print(f"    课程名称: {first_item['item']['课程名称']}")
            print(f"    RAG类型: {first_item['item']['rag_type']}")
            print(f"    模型名称: {first_item['item']['model_name']}")
            print(f"    提示词长度: {len(first_item['prompt'])} 字符")
            print(f"    录音文本长度: {len(first_item['transcript_content'])} 字符")
            
            # 检查占位符是否被正确替换
            if '{lesson_name}' in first_item['prompt']:
                print("  ✗ 警告: 课程名称占位符未被替换")
            else:
                print("  ✓ 课程名称占位符已正确替换")
                
            if '{transcript_content}' in first_item['prompt']:
                print("  ✗ 警告: 录音文本占位符未被替换")
            else:
                print("  ✓ 录音文本占位符已正确替换")
                
    except Exception as e:
        print(f"✗ 创建测试数据失败: {e}")
        return
    
    print("\n数据加载测试完成!")

if __name__ == "__main__":
    test_data_loading()
