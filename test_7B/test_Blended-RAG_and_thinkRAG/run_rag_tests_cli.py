#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
极简RAG测试系统
只做字符串拼接和模型调用
"""

import os
import sys
import json
import logging
import argparse
import time
import torch
import gc
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Tuple

# 设置环境变量
os.environ["FLASH_ATTENTION_DISABLE"] = "1"
os.environ["DISABLE_FLASH_ATTN"] = "1"
os.environ["HF_HUB_DISABLE_TELEMETRY"] = "1"
os.environ["TRANSFORMERS_VERBOSITY"] = "error"
os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"

# 固定的模型配置
MODELS = [
    {
        "name": "Qwen2.5-7B-Instruct",
        "path": "/home/<USER>/.cache/modelscope/hub/models/Qwen/Qwen2.5-7B-Instruct",
        "dir_name": "qwen25_7b"
    },
    {
        "name": "Qwen3-4B",
        "path": "/home/<USER>/.cache/modelscope/hub/models/Qwen/Qwen3-4B",
        "dir_name": "qwen3_4b"
    },
    {
        "name": "GLM-4-9B-0414",
        "path": "/home/<USER>/.cache/modelscope/hub/models/ZhipuAI/GLM-4-9B-0414",
        "dir_name": "glm4_9b"
    }
]

class SimpleModelTester:
    """极简模型测试器"""

    def __init__(self, model_name: str, model_path: str, device: str,
                 max_new_tokens: int = 16392, temperature: float = 0.1):
        self.model_name = model_name
        self.model_path = model_path
        self.device = device
        self.max_new_tokens = max_new_tokens
        self.temperature = temperature
        self.model = None
        self.tokenizer = None

    def load_model(self) -> bool:
        """加载模型"""
        try:
            from modelscope import AutoTokenizer, AutoModelForCausalLM

            logging.info(f"开始加载模型 {self.model_name} 到 {self.device}")

            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_path,
                trust_remote_code=True
            )

            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                torch_dtype=torch.float16,
                device_map=self.device,
                trust_remote_code=True
            )

            logging.info(f"✅ 模型 {self.model_name} 加载成功")
            return True

        except Exception as e:
            logging.error(f"❌ 模型 {self.model_name} 加载失败: {e}")
            return False

    def generate_response(self, prompt: str) -> str:
        """生成回复 - 使用与原项目相同的参数"""
        try:
            # 构建对话格式
            messages = [
                {"role": "system", "content": "你是一个教学分析专家。请直接输出分析结果，不要使用<think>标签或解释过程。"},
                {"role": "user", "content": prompt}
            ]

            # 应用聊天模板
            if hasattr(self.tokenizer, 'apply_chat_template'):
                formatted_prompt = self.tokenizer.apply_chat_template(
                    messages,
                    add_generation_prompt=True,
                    tokenize=False
                )
            else:
                formatted_prompt = prompt

            inputs = self.tokenizer(formatted_prompt, return_tensors="pt").to(self.device)

            # 使用与原项目相同的生成参数
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=min(self.max_new_tokens, 1024),  # 限制最大token数
                    temperature=self.temperature,
                    do_sample=True,
                    top_p=0.9,
                    repetition_penalty=1.1,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id,
                    use_cache=True
                )

            # 解码输出，只取新生成的部分
            input_length = inputs['input_ids'].shape[1]
            generated_tokens = outputs[0][input_length:]
            response = self.tokenizer.decode(generated_tokens, skip_special_tokens=True)

            return response.strip()

        except Exception as e:
            logging.error(f"生成回复失败: {e}")
            return ""

    def cleanup(self):
        """清理资源"""
        if self.model is not None:
            del self.model
            self.model = None
        if self.tokenizer is not None:
            del self.tokenizer
            self.tokenizer = None

        torch.cuda.empty_cache()
        gc.collect()
        logging.info(f"模型 {self.model_name} 资源清理完成")

def setup_logging():
    """设置日志配置"""
    log_dir = "test_7B/test_Blended-RAG_and_thinkRAG/logs"
    os.makedirs(log_dir, exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"rag_test_{timestamp}.log")

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

    # 设置第三方库的日志级别
    logging.getLogger("transformers").setLevel(logging.ERROR)
    logging.getLogger("torch").setLevel(logging.ERROR)
    logging.getLogger("modelscope").setLevel(logging.ERROR)

    logging.info(f"日志文件: {log_file}")

def load_transcript_files() -> Dict[str, Tuple[str, str]]:
    """
    加载录音文本文件

    Returns:
        Dict[lesson_id, (lesson_name, transcript_content)]
    """
    transcript_path = Path("/home/<USER>/test_models/download")
    transcript_files = {}

    for file_path in transcript_path.glob("*.txt"):
        filename = file_path.stem
        # 解析文件名：lesson_id_课程名称.txt
        if '_' in filename:
            lesson_id = filename.split('_')[0]
            lesson_name = '_'.join(filename.split('_')[1:])

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                transcript_files[lesson_id] = (lesson_name, content)
                logging.debug(f"加载录音文本: {lesson_id} - {lesson_name}")
            except Exception as e:
                logging.error(f"读取录音文本文件失败 {file_path}: {e}")

    logging.info(f"成功加载 {len(transcript_files)} 个录音文本文件")
    return transcript_files

def load_rag_prompts(rag_type: str, model_dir: str) -> Dict[str, str]:
    """
    加载RAG提示词文件

    Args:
        rag_type: "blended" 或 "think"
        model_dir: 模型目录名称 (如 "glm4_9b")

    Returns:
        Dict[lesson_id, prompt_content]
    """
    if rag_type == "blended":
        base_path = Path("/home/<USER>/test_models/cache/Blended-RAG_retrieval_results")
    elif rag_type == "think":
        base_path = Path("/home/<USER>/test_models/cache/thinkRAG_retrieval_results")
    else:
        raise ValueError(f"不支持的RAG类型: {rag_type}")

    model_path = base_path / model_dir
    if not model_path.exists():
        logging.warning(f"模型目录不存在: {model_path}")
        return {}

    prompts = {}
    for file_path in model_path.glob("*_enhanced_prompt.txt"):
        filename = file_path.stem
        lesson_id = filename.replace('_enhanced_prompt', '')

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            prompts[lesson_id] = content
            logging.debug(f"加载{rag_type}提示词: {lesson_id}")
        except Exception as e:
            logging.error(f"读取{rag_type}提示词文件失败 {file_path}: {e}")

    logging.info(f"成功加载 {len(prompts)} 个{rag_type}提示词文件 (模型: {model_dir})")
    return prompts

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="极简RAG测试系统")

    parser.add_argument("--max-new-tokens", type=int, default=1024,
                       help="最大新生成token数")
    parser.add_argument("--temperature", type=float, default=0.1,
                       help="生成温度")
    parser.add_argument("--mode", default="limit",
                       help="测试模式")
    parser.add_argument("--limit", type=int, default=10,
                       help="测试数量限制")

    return parser.parse_args()

def run_model_tests(model_config: Dict[str, str], test_data: List[Dict[str, Any]],
                   max_new_tokens: int, temperature: float) -> List[Dict[str, Any]]:
    """运行单个模型的测试"""
    model_name = model_config["name"]
    results = []

    # 选择GPU
    device = "cuda:0"  # 简单使用第一张GPU

    # 创建模型测试器
    tester = SimpleModelTester(
        model_name=model_name,
        model_path=model_config["path"],
        device=device,
        max_new_tokens=max_new_tokens,
        temperature=temperature
    )

    # 加载模型
    if not tester.load_model():
        logging.error(f"模型 {model_name} 加载失败，跳过测试")
        tester.cleanup()
        return results

    # 运行测试
    for i, test_item in enumerate(test_data):
        logging.info(f"测试 {model_name} - 第 {i+1}/{len(test_data)} 条")

        try:
            # 生成回复
            response = tester.generate_response(test_item['prompt'])

            # 保存结果
            result = {
                'item': test_item['item'],
                'prompt': test_item['prompt'][:700],  # 只保存前700字符
                'raw_response': response,
                'model_name': model_name,
                'timestamp': datetime.now().isoformat()
            }
            results.append(result)

            logging.info(f"✅ 测试完成: {test_item['item']['课例id']}")

        except Exception as e:
            logging.error(f"❌ 测试失败: {test_item['item']['课例id']} - {e}")
            continue

    # 清理模型
    tester.cleanup()

    logging.info(f"模型 {model_name} 测试完成，共 {len(results)} 条结果")
    return results

def create_test_data(rag_type: str, model_dir: str, limit: int = None) -> List[Dict[str, Any]]:
    """创建测试数据 - 只做简单的字符串拼接"""
    # 加载录音文本
    transcripts = load_transcript_files()

    # 加载RAG提示词
    prompts = load_rag_prompts(rag_type, model_dir)

    # 创建测试数据
    test_data = []
    for lesson_id in prompts.keys():
        if lesson_id in transcripts:
            lesson_name, transcript_content = transcripts[lesson_id]
            prompt_template = prompts[lesson_id]

            # 简单的字符串替换
            filled_prompt = prompt_template.replace('{lesson_name}', lesson_name)
            filled_prompt = filled_prompt.replace('{transcript_content}', transcript_content)

            test_item = {
                'item': {
                    '课例id': lesson_id,
                    '课程名称': lesson_name,
                    'rag_type': rag_type,
                    'model_dir': model_dir
                },
                'prompt': filled_prompt
            }
            test_data.append(test_item)

            if limit and len(test_data) >= limit:
                break

    logging.info(f"创建测试数据: {len(test_data)} 条 ({rag_type}RAG, {model_dir})")
    return test_data

def save_results(results: Dict[str, Any], output_dir: str):
    """保存测试结果"""
    os.makedirs(output_dir, exist_ok=True)

    # 保存总结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_file = os.path.join(output_dir, f"rag_test_summary_{timestamp}.json")

    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    logging.info(f"测试结果已保存到: {summary_file}")

    # 为每个模型和RAG类型保存单独的结果文件
    for model_name, model_results in results.items():
        for rag_type, rag_results in model_results.items():
            if isinstance(rag_results, list) and rag_results:
                result_file = os.path.join(output_dir, f"{model_name}_{rag_type}_{timestamp}.json")
                with open(result_file, 'w', encoding='utf-8') as f:
                    json.dump(rag_results, f, ensure_ascii=False, indent=2)
                logging.info(f"保存单独结果文件: {result_file}")

def main():
    """极简主函数 - 只做字符串拼接和模型调用"""
    args = parse_arguments()

    # 设置日志
    setup_logging()

    logging.info("=" * 60)
    logging.info("极简RAG测试系统启动")
    logging.info("=" * 60)
    logging.info(f"测试限制: {args.limit}")
    logging.info(f"温度参数: {args.temperature}")
    logging.info(f"最大token数: {args.max_new_tokens}")

    try:
        # 创建结果目录
        output_dir = "results"
        os.makedirs(output_dir, exist_ok=True)

        all_results = {}
        start_time = datetime.now()

        # 顺序测试每个模型
        for model_config in MODELS:
            model_name = model_config["name"]
            model_dir = model_config["dir_name"]

            logging.info(f"\n开始测试模型: {model_name}")
            all_results[model_name] = {}

            # 测试两种RAG类型
            for rag_type in ["blended", "think"]:
                logging.info(f"测试 {rag_type}RAG...")

                # 创建测试数据
                test_data = create_test_data(rag_type, model_dir, args.limit)

                if not test_data:
                    logging.warning(f"没有找到测试数据: {model_name} - {rag_type}")
                    all_results[model_name][f"{rag_type}RAG"] = []
                    continue

                # 运行测试
                try:
                    results = run_model_tests(
                        model_config=model_config,
                        test_data=test_data,
                        max_new_tokens=args.max_new_tokens,
                        temperature=args.temperature
                    )

                    all_results[model_name][f"{rag_type}RAG"] = results
                    logging.info(f"完成 {model_name} - {rag_type}RAG: {len(results)} 条结果")

                except Exception as e:
                    logging.error(f"测试失败 {model_name} - {rag_type}RAG: {e}")
                    all_results[model_name][f"{rag_type}RAG"] = []

        end_time = datetime.now()
        duration = end_time - start_time

        # 保存结果
        logging.info("\n保存测试结果...")
        save_results(all_results, output_dir)

        # 输出统计信息
        logging.info("=" * 60)
        logging.info("测试完成统计")
        logging.info("=" * 60)
        logging.info(f"总耗时: {duration}")

        total_tests = 0
        for model_name, model_results in all_results.items():
            for rag_type, rag_results in model_results.items():
                if isinstance(rag_results, list):
                    test_count = len(rag_results)
                    total_tests += test_count
                    logging.info(f"{model_name} - {rag_type}: {test_count} 条测试")

        logging.info(f"总测试数: {total_tests}")
        logging.info("RAG测试完成！")

    except KeyboardInterrupt:
        logging.info("用户中断测试")
        sys.exit(1)
    except Exception as e:
        logging.error(f"测试失败: {e}")
        logging.exception("详细错误:")
        sys.exit(1)

if __name__ == "__main__":
    main()
