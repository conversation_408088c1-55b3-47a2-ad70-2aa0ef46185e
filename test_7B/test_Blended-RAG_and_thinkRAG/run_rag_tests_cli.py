#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAG测试系统命令行接口
与原项目run_all_tests.py保持完全一致的命令行参数接口
"""

import os
import sys
import argparse
import logging
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from run_rag_tests import RAGDataLoader, RAGTestRunner, setup_logging

def parse_arguments():
    """解析命令行参数 - 与原项目完全一致"""
    parser = argparse.ArgumentParser(description="RAG测试系统 - 支持Blended-RAG和thinkRAG")
    
    # 基本参数
    parser.add_argument("--prompt-template", default="rag", 
                       help="提示词模板类型")
    parser.add_argument("--mode", default="limit",
                       help="测试模式")
    parser.add_argument("--limit", type=int, default=50,
                       help="测试数量限制")
    
    # GPU参数
    parser.add_argument("--gpus", type=int, default=4,
                       help="使用的GPU数量")
    parser.add_argument("--available-gpus", nargs="+", type=int,
                       help="可用的GPU ID列表")
    
    # 模型参数
    parser.add_argument("--models", nargs="+", 
                       default=["Qwen2.5-7B-Instruct", "Qwen3-4B", "GLM-4-9B-0414"],
                       help="要测试的模型列表")
    parser.add_argument("--merge-seconds", type=int, default=10,
                       help="录音文本合并时间间隔（秒）")
    parser.add_argument("--max-new-tokens", type=int, default=16392,
                       help="最大新生成token数")
    parser.add_argument("--temperature", type=float, default=0.1,
                       help="生成温度")
    
    # RAG特定参数
    parser.add_argument("--rag-types", nargs="+", 
                       default=["blended", "think"],
                       help="RAG类型列表")
    
    # 数据路径参数
    parser.add_argument("--blended-rag-path", 
                       default="/home/<USER>/test_models/cache/Blended-RAG_retrieval_results",
                       help="Blended-RAG结果目录路径")
    parser.add_argument("--think-rag-path",
                       default="/home/<USER>/test_models/cache/thinkRAG_retrieval_results", 
                       help="thinkRAG结果目录路径")
    parser.add_argument("--transcript-path",
                       default="/home/<USER>/test_models/download",
                       help="录音文本目录路径")
    
    # 输出参数
    parser.add_argument("--output-dir",
                       default="test_7B/test_Blended-RAG_and_thinkRAG/results",
                       help="结果输出目录")
    parser.add_argument("--log-level", default="INFO",
                       help="日志级别")
    
    return parser.parse_args()

def main():
    """主函数 - 与原项目保持一致的执行流程"""
    args = parse_arguments()
    
    # 设置日志
    setup_logging(args.log_level)
    
    logging.info("=" * 80)
    logging.info("RAG测试系统启动")
    logging.info("=" * 80)
    logging.info(f"提示词模板: {args.prompt_template}")
    logging.info(f"测试模式: {args.mode}")
    logging.info(f"测试限制: {args.limit}")
    logging.info(f"测试模型: {args.models}")
    logging.info(f"RAG类型: {args.rag_types}")
    logging.info(f"使用GPU数量: {args.gpus}")
    logging.info(f"可用GPU: {args.available_gpus}")
    logging.info(f"温度参数: {args.temperature}")
    logging.info(f"最大token数: {args.max_new_tokens}")
    logging.info(f"合并时间间隔: {args.merge_seconds}秒")
    
    try:
        # 初始化数据加载器
        logging.info("初始化数据加载器...")
        data_loader = RAGDataLoader(
            blended_rag_path=args.blended_rag_path,
            think_rag_path=args.think_rag_path,
            transcript_path=args.transcript_path
        )
        
        # 初始化测试运行器
        logging.info("初始化测试运行器...")
        test_runner = RAGTestRunner(
            num_gpus=args.gpus,
            available_gpus=args.available_gpus,
            merge_seconds=args.merge_seconds,
            max_new_tokens=args.max_new_tokens,
            temperature=args.temperature
        )
        
        # 运行测试
        logging.info("开始运行RAG测试...")
        start_time = datetime.now()
        
        results = test_runner.run_tests(
            test_models=args.models,
            rag_types=args.rag_types,
            data_loader=data_loader,
            limit=args.limit,
            mode=args.mode
        )
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # 保存结果
        logging.info("保存测试结果...")
        test_runner.save_results(results, args.output_dir)
        
        # 输出统计信息
        logging.info("=" * 80)
        logging.info("RAG测试完成统计")
        logging.info("=" * 80)
        logging.info(f"总耗时: {duration}")
        
        total_tests = 0
        successful_tests = 0
        
        for model_name, model_results in results.items():
            for rag_type, rag_results in model_results.items():
                if isinstance(rag_results, dict):
                    if "error" in rag_results:
                        logging.error(f"{model_name} - {rag_type}: 测试失败")
                    else:
                        test_count = len(rag_results)
                        total_tests += test_count
                        successful_tests += test_count
                        logging.info(f"{model_name} - {rag_type}: {test_count} 条测试成功")
        
        logging.info(f"总测试数: {total_tests}")
        logging.info(f"成功测试数: {successful_tests}")
        logging.info(f"成功率: {successful_tests/total_tests*100:.1f}%" if total_tests > 0 else "0%")
        
        logging.info("RAG测试系统运行完成")
        
    except KeyboardInterrupt:
        logging.info("用户中断测试")
        sys.exit(1)
    except Exception as e:
        logging.error(f"测试过程中发生错误: {e}")
        logging.exception("详细错误信息:")
        sys.exit(1)

if __name__ == "__main__":
    main()
