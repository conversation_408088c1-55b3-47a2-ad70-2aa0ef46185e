#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的RAG测试系统
专注于Blended-RAG和thinkRAG数据测试
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Tuple

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

# 设置环境变量
os.environ["FLASH_ATTENTION_DISABLE"] = "1"
os.environ["DISABLE_FLASH_ATTN"] = "1"
os.environ["HF_HUB_DISABLE_TELEMETRY"] = "1"
os.environ["TRANSFORMERS_VERBOSITY"] = "error"
os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"

from gpu_scheduler import GPUScheduler

# 固定的模型配置
MODELS = [
    {
        "name": "Qwen2.5-7B-Instruct",
        "organization": "Qwen",
        "parameters": "7B",
        "path": "/home/<USER>/.cache/modelscope/hub/models/Qwen/Qwen2.5-7B-Instruct",
        "dir_name": "qwen25_7b"
    },
    {
        "name": "Qwen3-4B",
        "organization": "Qwen",
        "parameters": "4B",
        "path": "/home/<USER>/.cache/modelscope/hub/models/Qwen/Qwen3-4B",
        "dir_name": "qwen3_4b"
    },
    {
        "name": "GLM-4-9B-0414",
        "organization": "ZhipuAI",
        "parameters": "9B",
        "path": "/home/<USER>/.cache/modelscope/hub/models/ZhipuAI/GLM-4-9B-0414",
        "dir_name": "glm4_9b"
    }
]

def setup_logging():
    """设置日志配置"""
    log_dir = "test_7B/test_Blended-RAG_and_thinkRAG/logs"
    os.makedirs(log_dir, exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"rag_test_{timestamp}.log")

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

    # 设置第三方库的日志级别
    logging.getLogger("transformers").setLevel(logging.ERROR)
    logging.getLogger("torch").setLevel(logging.ERROR)
    logging.getLogger("modelscope").setLevel(logging.ERROR)

    logging.info(f"日志文件: {log_file}")

def load_transcript_files() -> Dict[str, Tuple[str, str]]:
    """
    加载录音文本文件

    Returns:
        Dict[lesson_id, (lesson_name, transcript_content)]
    """
    transcript_path = Path("/home/<USER>/test_models/download")
    transcript_files = {}

    for file_path in transcript_path.glob("*.txt"):
        filename = file_path.stem
        # 解析文件名：lesson_id_课程名称.txt
        if '_' in filename:
            lesson_id = filename.split('_')[0]
            lesson_name = '_'.join(filename.split('_')[1:])

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                transcript_files[lesson_id] = (lesson_name, content)
                logging.debug(f"加载录音文本: {lesson_id} - {lesson_name}")
            except Exception as e:
                logging.error(f"读取录音文本文件失败 {file_path}: {e}")

    logging.info(f"成功加载 {len(transcript_files)} 个录音文本文件")
    return transcript_files

def load_rag_prompts(rag_type: str, model_dir: str) -> Dict[str, str]:
    """
    加载RAG提示词文件

    Args:
        rag_type: "blended" 或 "think"
        model_dir: 模型目录名称 (如 "glm4_9b")

    Returns:
        Dict[lesson_id, prompt_content]
    """
    if rag_type == "blended":
        base_path = Path("/home/<USER>/test_models/cache/Blended-RAG_retrieval_results")
    elif rag_type == "think":
        base_path = Path("/home/<USER>/test_models/cache/thinkRAG_retrieval_results")
    else:
        raise ValueError(f"不支持的RAG类型: {rag_type}")

    model_path = base_path / model_dir
    if not model_path.exists():
        logging.warning(f"模型目录不存在: {model_path}")
        return {}

    prompts = {}
    for file_path in model_path.glob("*_enhanced_prompt.txt"):
        filename = file_path.stem
        lesson_id = filename.replace('_enhanced_prompt', '')

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            prompts[lesson_id] = content
            logging.debug(f"加载{rag_type}提示词: {lesson_id}")
        except Exception as e:
            logging.error(f"读取{rag_type}提示词文件失败 {file_path}: {e}")

    logging.info(f"成功加载 {len(prompts)} 个{rag_type}提示词文件 (模型: {model_dir})")
    return prompts

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="简化的RAG测试系统")

    parser.add_argument("--max-new-tokens", type=int, default=16392,
                       help="最大新生成token数")
    parser.add_argument("--temperature", type=float, default=0.1,
                       help="生成温度")
    parser.add_argument("--mode", default="limit",
                       help="测试模式")
    parser.add_argument("--limit", type=int, default=50,
                       help="测试数量限制")

    return parser.parse_args()

def create_test_data(rag_type: str, model_dir: str, limit: int = None) -> List[Dict[str, Any]]:
    """
    创建测试数据

    Args:
        rag_type: "blended" 或 "think"
        model_dir: 模型目录名称
        limit: 限制数量

    Returns:
        测试数据列表
    """
    # 加载录音文本
    transcripts = load_transcript_files()

    # 加载RAG提示词
    prompts = load_rag_prompts(rag_type, model_dir)

    # 创建测试数据
    test_data = []
    for lesson_id in prompts.keys():
        if lesson_id in transcripts:
            lesson_name, transcript_content = transcripts[lesson_id]
            prompt_template = prompts[lesson_id]

            # 填充占位符
            filled_prompt = prompt_template.replace('{lesson_name}', lesson_name)
            filled_prompt = filled_prompt.replace('{transcript_content}', transcript_content)

            test_item = {
                'item': {
                    '课例id': lesson_id,
                    '课程名称': lesson_name,
                    'rag_type': rag_type,
                    'model_dir': model_dir
                },
                'prompt': filled_prompt,
                'transcript_content': transcript_content
            }
            test_data.append(test_item)

            if limit and len(test_data) >= limit:
                break

    logging.info(f"创建测试数据完成: {len(test_data)} 条 (RAG类型: {rag_type}, 模型: {model_dir})")
    return test_data

def save_results(results: Dict[str, Any], output_dir: str):
    """保存测试结果"""
    os.makedirs(output_dir, exist_ok=True)

    # 保存总结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_file = os.path.join(output_dir, f"rag_test_summary_{timestamp}.json")

    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    logging.info(f"测试结果已保存到: {summary_file}")

    # 为每个模型和RAG类型保存单独的结果文件
    for model_name, model_results in results.items():
        for rag_type, rag_results in model_results.items():
            if isinstance(rag_results, list) and rag_results:
                result_file = os.path.join(output_dir, f"{model_name}_{rag_type}_{timestamp}.json")
                with open(result_file, 'w', encoding='utf-8') as f:
                    json.dump(rag_results, f, ensure_ascii=False, indent=2)
                logging.info(f"保存单独结果文件: {result_file}")

def main():
    """主函数"""
    args = parse_arguments()

    # 设置日志
    setup_logging()

    logging.info("=" * 80)
    logging.info("简化RAG测试系统启动")
    logging.info("=" * 80)
    logging.info(f"测试模式: {args.mode}")
    logging.info(f"测试限制: {args.limit}")
    logging.info(f"温度参数: {args.temperature}")
    logging.info(f"最大token数: {args.max_new_tokens}")

    try:
        # 初始化GPU调度器
        logging.info("初始化GPU调度器...")
        gpu_scheduler = GPUScheduler(
            num_gpus=4,
            available_gpus=None,  # 自动检测
            merge_transcript=True,
            merge_seconds=10,
            max_new_tokens=args.max_new_tokens,
            temperature=args.temperature,
            prompt_template_type="rag"
        )

        # 创建结果目录
        output_dir = "test_7B/test_Blended-RAG_and_thinkRAG/results"
        os.makedirs(output_dir, exist_ok=True)

        all_results = {}
        start_time = datetime.now()

        # 测试每个模型
        for model_config in MODELS:
            model_name = model_config["name"]
            model_dir = model_config["dir_name"]

            logging.info(f"开始测试模型: {model_name}")
            all_results[model_name] = {}

            # 测试两种RAG类型
            for rag_type in ["blended", "think"]:
                logging.info(f"  测试 {rag_type}RAG...")

                # 创建测试数据
                test_data = create_test_data(rag_type, model_dir, args.limit)

                if not test_data:
                    logging.warning(f"  没有找到测试数据: {model_name} - {rag_type}")
                    all_results[model_name][f"{rag_type}RAG"] = []
                    continue

                # 运行测试
                try:
                    results = gpu_scheduler.run_parallel_tests(
                        model_configs=[model_config],
                        test_data=test_data,
                        test_mode=args.mode
                    )

                    all_results[model_name][f"{rag_type}RAG"] = results
                    logging.info(f"  完成测试 {model_name} - {rag_type}RAG: {len(results)} 条结果")

                except Exception as e:
                    logging.error(f"  测试失败 {model_name} - {rag_type}RAG: {e}")
                    all_results[model_name][f"{rag_type}RAG"] = []

        end_time = datetime.now()
        duration = end_time - start_time

        # 保存结果
        logging.info("保存测试结果...")
        save_results(all_results, output_dir)

        # 输出统计信息
        logging.info("=" * 80)
        logging.info("RAG测试完成统计")
        logging.info("=" * 80)
        logging.info(f"总耗时: {duration}")

        total_tests = 0
        successful_tests = 0

        for model_name, model_results in all_results.items():
            for rag_type, rag_results in model_results.items():
                if isinstance(rag_results, list):
                    test_count = len(rag_results)
                    total_tests += test_count
                    successful_tests += test_count
                    logging.info(f"{model_name} - {rag_type}: {test_count} 条测试成功")

        logging.info(f"总测试数: {total_tests}")
        logging.info(f"成功测试数: {successful_tests}")
        logging.info(f"成功率: {successful_tests/total_tests*100:.1f}%" if total_tests > 0 else "0%")

        logging.info("RAG测试系统运行完成")

    except KeyboardInterrupt:
        logging.info("用户中断测试")
        sys.exit(1)
    except Exception as e:
        logging.error(f"测试过程中发生错误: {e}")
        logging.exception("详细错误信息:")
        sys.exit(1)

if __name__ == "__main__":
    main()
