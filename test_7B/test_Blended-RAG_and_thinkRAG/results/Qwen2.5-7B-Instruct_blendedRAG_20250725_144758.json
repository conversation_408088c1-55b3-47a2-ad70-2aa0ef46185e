[{"item": {"课例id": "6b0afb5bd4e34194bcfffc8a7aa7f705", "课程名称": "6.陶罐和铁罐", "rag_type": "blended", "model_dir": "qwen25_7b"}, "prompt": "分析课堂录音，识别主要教学环节转换点。\n\n课程：6.陶罐和铁罐\n\n要求：\n1. 只识别教学环节转换点，不要逐句分析录音内容\n2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟\n3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）\n4. 时间点必须来自录音文本中的实际时间戳\n5. 环节名称必须使用具体的标准教学术语\n6. 严禁使用\"教学环节\"、\"具体环节名称\"等占位符，必须输出实际的环节名称\n7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并\n8. 忽略具体说话内容，只关注教学活动的重大转换\n9. 禁止对每个时间段进行详细描述，只标记主要教学阶段\n10. 一节课只有几个大的教学环节，不要把每句话都当作环节\n\n输出格式示例：\n00:XX:XX 具体环节名称;\n00:XX:XX 具体环节名称;\n00:XX:XX 具体环节名称;\n\n## Blended-RAG参考信息 \n以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：\n\n### 录音文本ID: 6b0afb5bd4e34194bcfffc8a7aa7f705\n### 模型: QWEN25_7B\n### 检索方法: Blended-RAG (BM25 + KNN + ELSER)\n\n以下是Blended-RAG多方法融合检索的结果：\n\n**检索结果 1** (融合分数: 0.290)\n文档ID: 6b0afb5bd4e34194bcfffc8a7aa7f705_keywords\n检索方法: KNN\n内容: 情境教学法,寓言,思维导图,角色扮演", "raw_response": "00:00:00; 导入及情境设置;\n00:05:40; 自主学习与初步探讨;\n00:10:04; 小组合作学习;\n00:17:04; 表演与角色扮演;\n00:28:02; 课堂总结与价值观引导;\n00:38:34; 课后作业布置;", "model_name": "Qwen2.5-7B-Instruct", "timestamp": "2025-07-25T14:46:03.163235"}, {"item": {"课例id": "feb44c3aebdd4d84b904d8364d07954e", "课程名称": "《课题1 分子和原子》", "rag_type": "blended", "model_dir": "qwen25_7b"}, "prompt": "分析课堂录音，识别主要教学环节转换点。\n\n课程：《课题1 分子和原子》\n\n要求：\n1. 只识别教学环节转换点，不要逐句分析录音内容\n2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟\n3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）\n4. 时间点必须来自录音文本中的实际时间戳\n5. 环节名称必须使用具体的标准教学术语\n6. 严禁使用\"教学环节\"、\"具体环节名称\"等占位符，必须输出实际的环节名称\n7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并\n8. 忽略具体说话内容，只关注教学活动的重大转换\n9. 禁止对每个时间段进行详细描述，只标记主要教学阶段\n10. 一节课只有几个大的教学环节，不要把每句话都当作环节\n\n输出格式示例：\n00:XX:XX 具体环节名称;\n00:XX:XX 具体环节名称;\n00:XX:XX 具体环节名称;\n\n## Blended-RAG参考信息 \n以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：\n\n### 录音文本ID: feb44c3aebdd4d84b904d8364d07954e\n### 模型: QWEN25_7B\n### 检索方法: Blended-RAG (BM25 + KNN + ELSER)\n\n以下是Blended-RAG多方法融合检索的结果：\n\n**检索结果 1** (融合分数: 0.266)\n文档ID: feb44c3aebdd4d84b904d8364d07954e_keywords\n检索方法: KNN\n内容: 翻转课堂, 分子特征, VR", "raw_response": "00:00:00;自主学习导入;\n00:03:28;课堂精准教学导入;\n00:07:56;学生作品展示与讲解;\n00:14:14;虚拟实验操作;\n00:23:05;实验方案设计与分享;\n00:34:06;微观角度解释实验过程;\n00:38:45;课堂检测与反馈;\n00:43:12;课程总结与互动;", "model_name": "Qwen2.5-7B-Instruct", "timestamp": "2025-07-25T14:46:07.424996"}]