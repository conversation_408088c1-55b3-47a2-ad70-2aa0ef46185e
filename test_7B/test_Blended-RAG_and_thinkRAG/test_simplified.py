#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化的RAG系统
"""

import os
import sys
import subprocess

def test_data_loading():
    """测试数据加载功能"""
    print("1. 测试数据加载功能...")
    
    # 添加当前目录到路径
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        from run_rag_tests_cli import load_transcript_files, load_rag_prompts, create_test_data
        
        # 测试录音文本加载
        transcripts = load_transcript_files()
        print(f"   ✓ 成功加载 {len(transcripts)} 个录音文本文件")
        
        # 测试RAG提示词加载
        for rag_type in ["blended", "think"]:
            for model_dir in ["glm4_9b", "qwen25_7b", "qwen3_4b"]:
                prompts = load_rag_prompts(rag_type, model_dir)
                print(f"   ✓ 成功加载 {len(prompts)} 个{rag_type}提示词 (模型: {model_dir})")
        
        # 测试创建测试数据
        test_data = create_test_data("blended", "glm4_9b", limit=2)
        print(f"   ✓ 成功创建 {len(test_data)} 条测试数据")
        
        if test_data:
            first_item = test_data[0]
            print(f"   示例数据:")
            print(f"     课例ID: {first_item['item']['课例id']}")
            print(f"     课程名称: {first_item['item']['课程名称']}")
            print(f"     RAG类型: {first_item['item']['rag_type']}")
            print(f"     提示词长度: {len(first_item['prompt'])} 字符")
            
            # 检查占位符是否被正确替换
            if '{lesson_name}' in first_item['prompt']:
                print("     ✗ 警告: 课程名称占位符未被替换")
            else:
                print("     ✓ 课程名称占位符已正确替换")
                
            if '{transcript_content}' in first_item['prompt']:
                print("     ✗ 警告: 录音文本占位符未被替换")
            else:
                print("     ✓ 录音文本占位符已正确替换")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 数据加载测试失败: {e}")
        return False

def test_command_line():
    """测试命令行接口"""
    print("2. 测试命令行接口...")
    
    try:
        # 测试帮助信息
        result = subprocess.run([
            sys.executable, "run_rag_tests_cli.py", "--help"
        ], capture_output=True, text=True, cwd=os.path.dirname(os.path.abspath(__file__)))
        
        if result.returncode == 0:
            print("   ✓ 命令行帮助信息正常")
            return True
        else:
            print(f"   ✗ 命令行帮助信息失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ✗ 命令行接口测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试简化的RAG系统...")
    print("=" * 50)
    
    success_count = 0
    total_tests = 2
    
    # 测试数据加载
    if test_data_loading():
        success_count += 1
    
    print()
    
    # 测试命令行接口
    if test_command_line():
        success_count += 1
    
    print()
    print("=" * 50)
    print(f"测试完成: {success_count}/{total_tests} 项测试通过")
    
    if success_count == total_tests:
        print("✓ 所有测试通过，系统准备就绪！")
        print()
        print("使用方法:")
        print("conda activate test_model")
        print("export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True")
        print("python test_7B/test_Blended-RAG_and_thinkRAG/run_rag_tests_cli.py --limit 10")
        return True
    else:
        print("✗ 部分测试失败，请检查系统配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
