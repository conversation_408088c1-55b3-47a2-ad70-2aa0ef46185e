# Blended-RAG和thinkRAG测试系统

基于test_7B项目架构开发的专门用于测试RAG增强提示词的系统。

## 功能特性

- 支持Blended-RAG和thinkRAG两种检索增强生成方法
- 完全复用test_7B项目的GPU分配逻辑和模型加载机制
- 支持三个指定模型：Qwen2.5-7B-Instruct、Qwen3-4B、GLM-4-9B-0414
- 自动填充录音文本内容到RAG提示词中
- 与原项目完全一致的JSON输出格式
- 支持多GPU并行处理
- 完整的日志系统和错误处理

## 项目结构

```
test_7B/test_Blended-RAG_and_thinkRAG/
├── run_rag_tests.py          # 核心RAG测试系统
├── run_rag_tests_cli.py      # 命令行接口（推荐使用）
├── test_data_loading.py      # 数据加载测试脚本
├── test_system.py           # 系统功能测试脚本
├── logs/                    # 日志文件目录
├── results/                 # 测试结果目录
└── README.md               # 本文档
```

## 数据源

- **Blended-RAG数据**: `/home/<USER>/test_models/cache/Blended-RAG_retrieval_results/`
- **thinkRAG数据**: `/home/<USER>/test_models/cache/thinkRAG_retrieval_results/`
- **录音文本**: `/home/<USER>/test_models/download/`

## 使用方法

### 1. 基本使用（推荐）

使用与原项目完全一致的命令行接口：

```bash
# 激活环境
conda activate test_model

# 设置环境变量
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True

# 运行RAG测试（与原项目命令格式完全一致）
python test_7B/test_Blended-RAG_and_thinkRAG/run_rag_tests_cli.py \
    --prompt-template rag \
    --mode limit \
    --limit 50 \
    --gpus 4 \
    --models "Qwen2.5-7B-Instruct" "Qwen3-4B" "GLM-4-9B-0414" \
    --merge-seconds 10 \
    --max-new-tokens 16392 \
    --temperature 0.1
```

### 2. 测试特定RAG类型

```bash
# 只测试Blended-RAG
python test_7B/test_Blended-RAG_and_thinkRAG/run_rag_tests_cli.py \
    --rag-types "blended" \
    --limit 10

# 只测试thinkRAG
python test_7B/test_Blended-RAG_and_thinkRAG/run_rag_tests_cli.py \
    --rag-types "think" \
    --limit 10

# 测试两种RAG类型
python test_7B/test_Blended-RAG_and_thinkRAG/run_rag_tests_cli.py \
    --rag-types "blended" "think" \
    --limit 20
```

### 3. 测试特定模型

```bash
# 只测试GLM-4-9B-0414模型
python test_7B/test_Blended-RAG_and_thinkRAG/run_rag_tests_cli.py \
    --models "GLM-4-9B-0414" \
    --limit 10

# 测试多个模型
python test_7B/test_Blended-RAG_and_thinkRAG/run_rag_tests_cli.py \
    --models "Qwen3-4B" "GLM-4-9B-0414" \
    --limit 20
```

### 4. 调整GPU配置

```bash
# 使用2张GPU
python test_7B/test_Blended-RAG_and_thinkRAG/run_rag_tests_cli.py \
    --gpus 2 \
    --limit 10

# 指定特定GPU
python test_7B/test_Blended-RAG_and_thinkRAG/run_rag_tests_cli.py \
    --gpus 2 \
    --available-gpus 0 1 \
    --limit 10
```

## 测试和验证

### 1. 数据加载测试

```bash
cd test_7B/test_Blended-RAG_and_thinkRAG
python test_data_loading.py
```

### 2. 系统功能测试

```bash
cd test_7B/test_Blended-RAG_and_thinkRAG
python test_system.py
```

## 输出格式

系统输出的JSON文件格式与原项目完全一致，包括：

- 每个模型和RAG类型的单独结果文件
- 综合统计结果文件
- 详细的测试日志

## 技术参数

- **支持模型**: Qwen2.5-7B-Instruct, Qwen3-4B, GLM-4-9B-0414
- **默认温度**: 0.1
- **默认token限制**: 16392
- **默认合并间隔**: 10秒
- **默认GPU数量**: 4张

## 注意事项

1. 确保已激活正确的conda环境：`conda activate test_model`
2. 设置必要的环境变量：`PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True`
3. 确保有足够的GPU内存用于模型加载
4. 系统会自动检测可用GPU并避开被占用的GPU（如GPU 2和3）

## 故障排除

如果遇到问题，请检查：

1. 数据路径是否正确
2. GPU内存是否充足
3. 模型文件是否存在
4. 网络连接是否正常（用于模型下载）

## 与原项目的兼容性

本系统完全兼容原test_7B项目的：

- 命令行参数格式
- GPU分配策略
- JSON输出格式
- 日志系统
- 错误处理机制
