#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Blended-RAG和thinkRAG测试系统
基于test_7B项目架构，专门用于测试RAG增强的提示词
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime
from typing import List, Dict, Any, Tuple
from pathlib import Path

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

# 在导入任何其他库之前设置环境变量
os.environ["FLASH_ATTENTION_DISABLE"] = "1"
os.environ["DISABLE_FLASH_ATTN"] = "1"
os.environ["HF_HUB_DISABLE_TELEMETRY"] = "1"
os.environ["TRANSFORMERS_VERBOSITY"] = "error"
os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"

from gpu_scheduler import GPUScheduler

class RAGDataLoader:
    """RAG数据加载器"""
    
    def __init__(self, blended_rag_path: str, think_rag_path: str, transcript_path: str):
        """
        初始化RAG数据加载器
        
        Args:
            blended_rag_path: Blended-RAG结果目录路径
            think_rag_path: thinkRAG结果目录路径  
            transcript_path: 录音文本目录路径
        """
        self.blended_rag_path = Path(blended_rag_path)
        self.think_rag_path = Path(think_rag_path)
        self.transcript_path = Path(transcript_path)
        
        # 验证路径存在
        for path in [self.blended_rag_path, self.think_rag_path, self.transcript_path]:
            if not path.exists():
                raise FileNotFoundError(f"路径不存在: {path}")
                
        logging.info(f"RAG数据加载器初始化完成")
        logging.info(f"Blended-RAG路径: {self.blended_rag_path}")
        logging.info(f"thinkRAG路径: {self.think_rag_path}")
        logging.info(f"录音文本路径: {self.transcript_path}")
    
    def load_transcript_files(self) -> Dict[str, Tuple[str, str]]:
        """
        加载录音文本文件
        
        Returns:
            Dict[lesson_id, (lesson_name, transcript_content)]
        """
        transcript_files = {}
        
        for file_path in self.transcript_path.glob("*.txt"):
            filename = file_path.stem
            # 解析文件名：lesson_id_课程名称.txt
            if '_' in filename:
                lesson_id = filename.split('_')[0]
                lesson_name = '_'.join(filename.split('_')[1:])
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                    transcript_files[lesson_id] = (lesson_name, content)
                    logging.debug(f"加载录音文本: {lesson_id} - {lesson_name}")
                except Exception as e:
                    logging.error(f"读取录音文本文件失败 {file_path}: {e}")
                    
        logging.info(f"成功加载 {len(transcript_files)} 个录音文本文件")
        return transcript_files
    
    def load_rag_prompts(self, rag_type: str, model_name: str) -> Dict[str, str]:
        """
        加载RAG提示词文件
        
        Args:
            rag_type: "blended" 或 "think"
            model_name: 模型名称 (如 "glm4_9b")
            
        Returns:
            Dict[lesson_id, prompt_content]
        """
        if rag_type == "blended":
            base_path = self.blended_rag_path
        elif rag_type == "think":
            base_path = self.think_rag_path
        else:
            raise ValueError(f"不支持的RAG类型: {rag_type}")
            
        model_path = base_path / model_name
        if not model_path.exists():
            logging.warning(f"模型目录不存在: {model_path}")
            return {}
            
        prompts = {}
        for file_path in model_path.glob("*_enhanced_prompt.txt"):
            filename = file_path.stem
            lesson_id = filename.replace('_enhanced_prompt', '')
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                prompts[lesson_id] = content
                logging.debug(f"加载{rag_type}提示词: {lesson_id}")
            except Exception as e:
                logging.error(f"读取{rag_type}提示词文件失败 {file_path}: {e}")
                
        logging.info(f"成功加载 {len(prompts)} 个{rag_type}提示词文件 (模型: {model_name})")
        return prompts
    
    def create_test_data(self, rag_type: str, model_name: str, limit: int = None) -> List[Dict[str, Any]]:
        """
        创建测试数据
        
        Args:
            rag_type: "blended" 或 "think"
            model_name: 模型名称
            limit: 限制数量
            
        Returns:
            测试数据列表
        """
        # 加载录音文本
        transcripts = self.load_transcript_files()
        
        # 加载RAG提示词
        prompts = self.load_rag_prompts(rag_type, model_name)
        
        # 创建测试数据
        test_data = []
        for lesson_id in prompts.keys():
            if lesson_id in transcripts:
                lesson_name, transcript_content = transcripts[lesson_id]
                prompt_template = prompts[lesson_id]
                
                # 填充占位符
                filled_prompt = prompt_template.replace('{lesson_name}', lesson_name)
                filled_prompt = filled_prompt.replace('{transcript_content}', transcript_content)
                
                test_item = {
                    'item': {
                        '课例id': lesson_id,
                        '课程名称': lesson_name,
                        'rag_type': rag_type,
                        'model_name': model_name
                    },
                    'prompt': filled_prompt,
                    'transcript_content': transcript_content
                }
                test_data.append(test_item)
                
                if limit and len(test_data) >= limit:
                    break
                    
        logging.info(f"创建测试数据完成: {len(test_data)} 条 (RAG类型: {rag_type}, 模型: {model_name})")
        return test_data

class RAGTestRunner:
    """RAG测试运行器"""
    
    def __init__(self, num_gpus: int = 4, available_gpus: List[int] = None,
                 merge_seconds: int = 10, max_new_tokens: int = 16392, 
                 temperature: float = 0.1):
        """
        初始化RAG测试运行器
        
        Args:
            num_gpus: 使用的GPU数量
            available_gpus: 可用的GPU ID列表
            merge_seconds: 合并时间间隔（秒）
            max_new_tokens: 最大新生成token数
            temperature: 生成温度
        """
        self.model_configs = [
            {
                "name": "Qwen2.5-7B-Instruct",
                "organization": "Qwen",
                "parameters": "7B",
                "path": "/home/<USER>/.cache/modelscope/hub/models/Qwen/Qwen2.5-7B-Instruct"
            },
            {
                "name": "Qwen3-4B", 
                "organization": "Qwen",
                "parameters": "4B",
                "path": "/home/<USER>/.cache/modelscope/hub/models/Qwen/Qwen3-4B"
            },
            {
                "name": "GLM-4-9B-0414",
                "organization": "ZhipuAI", 
                "parameters": "9B",
                "path": "/home/<USER>/.cache/modelscope/hub/models/ZhipuAI/GLM-4-9B-0414"
            }
        ]
        
        # 初始化GPU调度器
        self.gpu_scheduler = GPUScheduler(
            num_gpus=num_gpus,
            available_gpus=available_gpus,
            merge_transcript=True,  # 始终启用录音文本合并
            merge_seconds=merge_seconds,
            max_new_tokens=max_new_tokens,
            temperature=temperature,
            prompt_template_type="rag"  # 使用RAG模板类型
        )
        
        # 创建必要的目录
        os.makedirs("test_7B/test_Blended-RAG_and_thinkRAG/logs", exist_ok=True)
        os.makedirs("test_7B/test_Blended-RAG_and_thinkRAG/results", exist_ok=True)
        
        logging.info(f"RAG测试运行器初始化完成 - 使用 {num_gpus} 张GPU")
        logging.info(f"支持模型: {[config['name'] for config in self.model_configs]}")

    def run_tests(self, test_models: List[str], rag_types: List[str],
                  data_loader: RAGDataLoader, limit: int = None,
                  mode: str = "limit") -> Dict[str, Any]:
        """
        运行RAG测试

        Args:
            test_models: 要测试的模型列表
            rag_types: RAG类型列表 ["blended", "think"]
            data_loader: 数据加载器
            limit: 测试数量限制
            mode: 测试模式

        Returns:
            测试结果字典
        """
        all_results = {}

        # 模型名称映射（用于文件路径）
        model_name_mapping = {
            "Qwen2.5-7B-Instruct": "qwen25_7b",
            "Qwen3-4B": "qwen3_4b",
            "GLM-4-9B-0414": "glm4_9b"
        }

        for model_name in test_models:
            if model_name not in [config["name"] for config in self.model_configs]:
                logging.warning(f"不支持的模型: {model_name}")
                continue

            model_key = model_name_mapping.get(model_name, model_name.lower())
            all_results[model_name] = {}

            for rag_type in rag_types:
                logging.info(f"开始测试 {model_name} - {rag_type}RAG")

                # 创建测试数据
                test_data = data_loader.create_test_data(rag_type, model_key, limit)

                if not test_data:
                    logging.warning(f"没有找到测试数据: {model_name} - {rag_type}")
                    continue

                # 运行测试
                try:
                    results = self.gpu_scheduler.run_parallel_tests(
                        model_configs=[config for config in self.model_configs
                                     if config["name"] == model_name],
                        test_data=test_data,
                        test_mode="limit"
                    )

                    all_results[model_name][f"{rag_type}RAG"] = results
                    logging.info(f"完成测试 {model_name} - {rag_type}RAG: {len(results)} 条结果")

                except Exception as e:
                    logging.error(f"测试失败 {model_name} - {rag_type}RAG: {e}")
                    all_results[model_name][f"{rag_type}RAG"] = {"error": str(e)}

        return all_results

    def save_results(self, results: Dict[str, Any], output_dir: str):
        """
        保存测试结果

        Args:
            results: 测试结果
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)

        # 保存总结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_file = os.path.join(output_dir, f"rag_test_summary_{timestamp}.json")

        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        logging.info(f"测试结果已保存到: {summary_file}")

        # 为每个模型和RAG类型保存单独的结果文件
        for model_name, model_results in results.items():
            for rag_type, rag_results in model_results.items():
                if isinstance(rag_results, dict) and "error" not in rag_results:
                    result_file = os.path.join(output_dir, f"{model_name}_{rag_type}_{timestamp}.json")
                    with open(result_file, 'w', encoding='utf-8') as f:
                        json.dump(rag_results, f, ensure_ascii=False, indent=2)
                    logging.info(f"保存单独结果文件: {result_file}")

def setup_logging(log_level: str = "INFO"):
    """设置日志配置"""
    log_dir = "test_7B/test_Blended-RAG_and_thinkRAG/logs"
    os.makedirs(log_dir, exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"rag_test_{timestamp}.log")

    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

    # 设置第三方库的日志级别
    logging.getLogger("transformers").setLevel(logging.ERROR)
    logging.getLogger("torch").setLevel(logging.ERROR)
    logging.getLogger("modelscope").setLevel(logging.ERROR)

    logging.info(f"日志文件: {log_file}")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Blended-RAG和thinkRAG测试系统")

    # 基本参数
    parser.add_argument("--models", nargs="+",
                       default=["Qwen2.5-7B-Instruct", "Qwen3-4B", "GLM-4-9B-0414"],
                       help="要测试的模型列表")
    parser.add_argument("--rag-types", nargs="+",
                       default=["blended", "think"],
                       help="RAG类型列表")
    parser.add_argument("--limit", type=int, default=None,
                       help="测试数量限制")
    parser.add_argument("--mode", default="limit",
                       help="测试模式")

    # GPU参数
    parser.add_argument("--gpus", type=int, default=4,
                       help="使用的GPU数量")
    parser.add_argument("--available-gpus", nargs="+", type=int,
                       help="可用的GPU ID列表")

    # 模型参数
    parser.add_argument("--merge-seconds", type=int, default=10,
                       help="录音文本合并时间间隔（秒）")
    parser.add_argument("--max-new-tokens", type=int, default=16392,
                       help="最大新生成token数")
    parser.add_argument("--temperature", type=float, default=0.1,
                       help="生成温度")

    # 数据路径参数
    parser.add_argument("--blended-rag-path",
                       default="/home/<USER>/test_models/cache/Blended-RAG_retrieval_results",
                       help="Blended-RAG结果目录路径")
    parser.add_argument("--think-rag-path",
                       default="/home/<USER>/test_models/cache/thinkRAG_retrieval_results",
                       help="thinkRAG结果目录路径")
    parser.add_argument("--transcript-path",
                       default="/home/<USER>/test_models/download",
                       help="录音文本目录路径")

    # 输出参数
    parser.add_argument("--output-dir",
                       default="test_7B/test_Blended-RAG_and_thinkRAG/results",
                       help="结果输出目录")
    parser.add_argument("--log-level", default="INFO",
                       help="日志级别")

    return parser.parse_args()

def main():
    """主函数"""
    args = parse_arguments()

    # 设置日志
    setup_logging(args.log_level)

    logging.info("=" * 80)
    logging.info("开始RAG测试系统")
    logging.info("=" * 80)
    logging.info(f"测试模型: {args.models}")
    logging.info(f"RAG类型: {args.rag_types}")
    logging.info(f"测试限制: {args.limit}")
    logging.info(f"使用GPU数量: {args.gpus}")
    logging.info(f"可用GPU: {args.available_gpus}")
    logging.info(f"温度参数: {args.temperature}")
    logging.info(f"最大token数: {args.max_new_tokens}")
    logging.info(f"合并时间间隔: {args.merge_seconds}秒")

    try:
        # 初始化数据加载器
        logging.info("初始化数据加载器...")
        data_loader = RAGDataLoader(
            blended_rag_path=args.blended_rag_path,
            think_rag_path=args.think_rag_path,
            transcript_path=args.transcript_path
        )

        # 初始化测试运行器
        logging.info("初始化测试运行器...")
        test_runner = RAGTestRunner(
            num_gpus=args.gpus,
            available_gpus=args.available_gpus,
            merge_seconds=args.merge_seconds,
            max_new_tokens=args.max_new_tokens,
            temperature=args.temperature
        )

        # 运行测试
        logging.info("开始运行测试...")
        start_time = datetime.now()

        results = test_runner.run_tests(
            test_models=args.models,
            rag_types=args.rag_types,
            data_loader=data_loader,
            limit=args.limit,
            mode=args.mode
        )

        end_time = datetime.now()
        duration = end_time - start_time

        # 保存结果
        logging.info("保存测试结果...")
        test_runner.save_results(results, args.output_dir)

        # 输出统计信息
        logging.info("=" * 80)
        logging.info("测试完成统计")
        logging.info("=" * 80)
        logging.info(f"总耗时: {duration}")

        total_tests = 0
        successful_tests = 0

        for model_name, model_results in results.items():
            for rag_type, rag_results in model_results.items():
                if isinstance(rag_results, dict):
                    if "error" in rag_results:
                        logging.error(f"{model_name} - {rag_type}: 测试失败")
                    else:
                        test_count = len(rag_results)
                        total_tests += test_count
                        successful_tests += test_count
                        logging.info(f"{model_name} - {rag_type}: {test_count} 条测试成功")

        logging.info(f"总测试数: {total_tests}")
        logging.info(f"成功测试数: {successful_tests}")
        logging.info(f"成功率: {successful_tests/total_tests*100:.1f}%" if total_tests > 0 else "0%")

        logging.info("RAG测试系统运行完成")

    except KeyboardInterrupt:
        logging.info("用户中断测试")
        sys.exit(1)
    except Exception as e:
        logging.error(f"测试过程中发生错误: {e}")
        logging.exception("详细错误信息:")
        sys.exit(1)

if __name__ == "__main__":
    main()
