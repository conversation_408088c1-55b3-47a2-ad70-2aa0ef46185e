#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试RAG系统的完整功能
"""

import os
import sys
import logging
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from run_rag_tests import RAGDataLoader, RAGTestRunner, setup_logging

def test_rag_system():
    """测试RAG系统的完整功能"""
    print("开始测试RAG系统...")
    
    # 设置日志
    setup_logging("INFO")
    
    try:
        # 初始化数据加载器
        print("1. 初始化数据加载器...")
        data_loader = RAGDataLoader(
            blended_rag_path="/home/<USER>/test_models/cache/Blended-RAG_retrieval_results",
            think_rag_path="/home/<USER>/test_models/cache/thinkRAG_retrieval_results",
            transcript_path="/home/<USER>/test_models/download"
        )
        print("✓ 数据加载器初始化成功")
        
        # 初始化测试运行器
        print("2. 初始化测试运行器...")
        test_runner = RAGTestRunner(
            num_gpus=2,  # 使用2张GPU进行测试
            available_gpus=None,  # 自动检测
            merge_seconds=10,
            max_new_tokens=16392,
            temperature=0.1
        )
        print("✓ 测试运行器初始化成功")
        
        # 运行小规模测试
        print("3. 运行小规模测试...")
        start_time = datetime.now()
        
        results = test_runner.run_tests(
            test_models=["Qwen2.5-7B-Instruct"],  # 只测试一个模型
            rag_types=["blended"],  # 只测试一种RAG类型
            data_loader=data_loader,
            limit=2,  # 只测试2条数据
            mode="limit"
        )
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # 保存结果
        print("4. 保存测试结果...")
        test_runner.save_results(results, "test_7B/test_Blended-RAG_and_thinkRAG/test_results")
        
        # 输出统计信息
        print("5. 测试完成统计:")
        print(f"   总耗时: {duration}")
        
        for model_name, model_results in results.items():
            for rag_type, rag_results in model_results.items():
                if isinstance(rag_results, dict) and "error" not in rag_results:
                    print(f"   {model_name} - {rag_type}: {len(rag_results)} 条测试成功")
                else:
                    print(f"   {model_name} - {rag_type}: 测试失败")
        
        print("✓ RAG系统测试完成!")
        return True
        
    except Exception as e:
        print(f"✗ RAG系统测试失败: {e}")
        logging.exception("详细错误信息:")
        return False

if __name__ == "__main__":
    success = test_rag_system()
    sys.exit(0 if success else 1)
