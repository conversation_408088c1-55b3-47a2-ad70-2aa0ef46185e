分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: ab7b02c6d8a44c9fafc0b44ee7900018
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.225)
文档ID: ab7b02c6d8a44c9fafc0b44ee7900018_keywords
检索方法: KNN
内容: 技术融合, 文本解读, 字词教学, 角色扮演, 情境创设, 识字写字, 深度学习, 探究氛围

**检索结果 2** (融合分数: 0.201)
文档ID: ab7b02c6d8a44c9fafc0b44ee7900018_subject_features
检索方法: KNN
内容: 学科：语文  
特征1：字词教学与文本解读相结合 | 通过动态GIF导入、形声字规律识字、动作演示等方法，将字词教学融入文本解读，帮助学生建立字词与意义的联系，提升语言基础能力。  
特征2：技术工具辅助教学 | 利用电子白板、乐客网、PPT等技术工具，通过动态标注、蒙层功能等增强直观性，提高课堂互动性和趣味性，适应信息化教学趋势。  
特征3：情境创设与角色扮演 | 通过“小鸟”“青蛙”对话的角...

**检索结果 3** (融合分数: 0.192)
文档ID: ab7b02c6d8a44c9fafc0b44ee7900018_teaching_modes
检索方法: KNN
内容: 教学模式1: 0.8, 识别依据：该摘要体现了“建构主义学习理论”，通过技术工具（电子白板、乐客网等）和互动活动（角色扮演、写画吧等）引导学生主动探究和意义建构，强调学生的主体性和合作学习。
教学模式2: 0.7, 识别依据：该摘要体现了“探究式学习模式”，通过动态标注、实时反馈和情境创设等策略，鼓励学生自主发现和解决问题，如“连连看”游戏和“写小故事”任务，培养学生的探究能力。
教学模式3: 0...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}