分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: eceecc9adfab417c8ce326298f8c5982
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.207)
文档ID: eceecc9adfab417c8ce326298f8c5982_keywords
检索方法: KNN
内容: 文本解读, 信息技术, 审美教育, 沉浸式场景, 角色扮演, 游戏化设计, 自然美学, 深度学习

**检索结果 2** (融合分数: 0.182)
文档ID: eceecc9adfab417c8ce326298f8c5982_teaching_modes
检索方法: KNN
内容: 教学模式1: 深度学习模式, 0.9, 教学摘要强调从文本感知到情感升华的递进过程，融合多维度教学策略（如情境教学、角色扮演、开放性问题）促进学生的深度理解和情感体验。
教学模式2: 技术增强型教学模式, 0.8, 明确提及信息技术（如交互式电子白板、VR视频、APP）与传统教具的结合，通过技术手段构建沉浸式学习环境，提升教学效果。
教学模式3: 游戏化教学模式, 0.7, 教学设计中融入"读词摘...

**检索结果 3** (融合分数: 0.170)
文档ID: eceecc9adfab417c8ce326298f8c5982_subject_features
检索方法: KNN
内容: 学科：语文（小学高年级或初中阶段）

特征1：文本解读与审美教育融合 | 通过对《荷花》文本的细致解读，结合VR实景视频、古诗导入等手段，将语言文字的学习与审美体验相结合，提升学生对文学作品的感知能力和审美情趣。

特征2：信息技术与多元教学工具结合 | 运用交互式电子白板、班级优化大师APP、PPT等现代信息技术工具，以及传统教具如肢体动作示范，形成技术与传统教学方法的互补，增强教学的互动性和趣...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}