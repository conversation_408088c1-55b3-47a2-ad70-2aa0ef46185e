分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: fafe80047f854229839fb50a6d9a8710
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.263)
文档ID: fafe80047f854229839fb50a6d9a8710_keywords
检索方法: KNN
内容: 分式概念, 分母含字母, 对比教学, 分组竞赛, 分式运算, 分层提问, 教学互动, 技术辅助

**检索结果 2** (融合分数: 0.201)
文档ID: fafe80047f854229839fb50a6d9a8710_teaching_modes
检索方法: KNN
内容: 教学模式1: 递进式探究教学, 0.85, 教学流程呈递进式推进，通过核心差异导入、关键追问、案例分析等方式逐步深化学生对分式概念与性质的理解，强调知识的逐步构建和辨析。
教学模式2: 合作学习, 0.75, 结合班级优化大师开展分组竞赛，学生在分组中主动纠错、深度思考，体现了合作学习的特点。
教学模式3: 多媒体辅助教学, 0.80, 运用智慧授课助手、西卧白板、PPT、实物投影等技术工具，结合...

**检索结果 3** (融合分数: 0.200)
文档ID: fafe80047f854229839fb50a6d9a8710_subject_features
检索方法: KNN
内容: 学科：数学  
特征1：概念辨析与对比教学 | 通过对比分式与整式、分母含字母与分母为数字的差异，帮助学生精准理解分式定义，培养数学概念的严谨性。  
特征2：技术辅助教学 | 利用智慧授课助手、班级优化大师、西卧白板等工具，增强互动性和可视化效果，提高课堂参与度和学习效率。  
特征3：分层教学与即时反馈 | 通过分层提问和即时反馈，兼顾不同层次学生的需求，促进个性化学习，同时强化知识内化。  ...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}