分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: a20c632931d24bcbaf849fc9e147ed60
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.204)
文档ID: a20c632931d24bcbaf849fc9e147ed60_keywords
检索方法: KNN
内容: STEAM课程, 探究式学习, 制作小船, 设计流程, 多媒体技术, 师生互动, 工程原理, 开放式提问

**检索结果 2** (融合分数: 0.203)
文档ID: a20c632931d24bcbaf849fc9e147ed60_subject_features
检索方法: KNN
内容: 学科：STEAM（科学、技术、工程、艺术、数学交叉融合）

特征1：跨学科整合 | 教学价值：本节课将科学（浮力原理）、技术（电路连接与动力系统）、工程（结构设计与稳定性分析）、艺术（创意小船外观设计）和数学（比例计算与数据分析）紧密结合，帮助学生理解知识间的内在联系，培养综合运用多学科知识解决实际问题的能力。

特征2：探究式学习 | 教学价值：通过问题链引导（如“小船易倾倒”），学生自主设计、...

**检索结果 3** (融合分数: 0.188)
文档ID: a20c632931d24bcbaf849fc9e147ed60_teaching_modes
检索方法: KNN
内容: 教学模式1: 探究式学习, 1.0  
识别依据：整个教学过程围绕"制作创意小船"展开，学生通过设计、制作、测试、反馈等环节自主探究，教师仅提供引导和资源支持，符合探究式学习的核心特征。

教学模式2: 项目式学习, 0.9  
识别依据：课程以一个完整的项目（制作创意小船）为主线，涵盖设计、制作、展示、反馈等完整流程，强调跨学科知识的应用和综合能力培养，符合项目式学习的定义。

教学模式3: 合...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}