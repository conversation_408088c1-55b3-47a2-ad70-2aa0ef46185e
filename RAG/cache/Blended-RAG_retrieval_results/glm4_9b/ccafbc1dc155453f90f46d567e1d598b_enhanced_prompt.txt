分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: ccafbc1dc155453f90f46d567e1d598b
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.262)
文档ID: ccafbc1dc155453f90f46d567e1d598b_keywords
检索方法: KNN
内容: 小数概念, 教学模式, 生活情境, 动态演示, 单位换算, 分数转换, 互动问答, 探究性教学

**检索结果 2** (融合分数: 0.200)
文档ID: ccafbc1dc155453f90f46d567e1d598b_subject_features
检索方法: KNN
内容: 学科：数学  
特征1：生活化情境导入 | 通过动物园游览等生活化情境导入小数概念，使抽象数学知识与实际生活联系紧密，激发学生兴趣，增强学习动机。  
特征2：多媒体技术辅助教学 | 运用PPT动态演示、动画、实物测量等手段构建知识网络，直观展示小数原理和结构，帮助学生建立清晰的认知模型。  
特征3：分层递进式练习设计 | 从基础单位换算到综合应用，结合判断题、选择题等形式检测理解深度，逐步提升...

**检索结果 3** (融合分数: 0.195)
文档ID: ccafbc1dc155453f90f46d567e1d598b_teaching_modes
检索方法: KNN
内容: 教学模式1: 探究性学习, 0.85, 识别依据：摘要中多次提到“探究性教学特征”，如“启发式提问”、“实例分析”、“学生积极回应”等，以及“知识建构过程清晰完整”，表明学生通过主动探索和问题解决来构建知识。
教学模式2: 合作学习, 0.75, 识别依据：摘要提到“小组讨论”、“学生互动问答”，如“针对‘0.3米中的3代表什么’的讨论”，显示学生通过合作和交流来深化理解。
教学模式3: 多元化教...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}