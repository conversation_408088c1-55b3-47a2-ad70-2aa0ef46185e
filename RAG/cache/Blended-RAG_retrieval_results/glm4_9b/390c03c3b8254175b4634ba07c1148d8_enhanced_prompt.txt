分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 390c03c3b8254175b4634ba07c1148d8
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.203)
文档ID: 390c03c3b8254175b4634ba07c1148d8_keywords
检索方法: KNN
内容: 文言文, 任务驱动, 技术赋能, 教学模式, 字形结构, 互动教学, 批判思维, 情境创设

**检索结果 2** (融合分数: 0.198)
文档ID: 390c03c3b8254175b4634ba07c1148d8_subject_features
检索方法: KNN
内容: 学科：文言文（中国古典文学）

特征1：情境导入与历史背景结合 | 通过"盲人摸象""曹冲称象"谜语激活故事认知，结合微课视频呈现司马光历史形象，建立人物认知。这种教学方式能激发学生兴趣，增强历史人物的形象感，使抽象的文字学习变得生动具体，有助于学生更好地理解和记忆文言文内容。

特征2：技术赋能与多元互动 | 运用平板电脑实现课前大数据分析预习成效、课堂放大镜功能指导朗读、课后平台完成故事创作并...

**检索结果 3** (融合分数: 0.194)
文档ID: 390c03c3b8254175b4634ba07c1148d8_teaching_modes
检索方法: KNN
内容: 教学模式1: 建构主义, 0.9, 识别依据：摘要强调学生通过自主探究、任务驱动和上下文联系等方式建构知识体系，如"学生在自主探究中建构知识体系"、"通过联系上下文达成共识"，符合建构主义的核心思想。
教学模式2: 混合式学习, 0.8, 识别依据：摘要明确提到"技术+方法+内容"三维融合，利用平板、大数据分析等技术手段支持课前、课中、课后学习，如"平板实现三重赋能"、"依托平台完成故事创作"，体...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}