分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 82f078848b5840a49938cd0344d8ae68
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.230)
文档ID: 82f078848b5840a49938cd0344d8ae68_keywords
检索方法: KNN
内容: Python编程, 任务驱动法, 小组协作, 程序开发, 教学流程, 技术工具, 互动教学, 生活化案例

**检索结果 2** (融合分数: 0.186)
文档ID: 82f078848b5840a49938cd0344d8ae68_subject_features
检索方法: KNN
内容: 学科：计算机科学与技术（编程教育）

特征1：实践导向与项目驱动 | 通过智能生产线项目和智能感应消毒喷壶等实际案例，让学生在实践中学习编程，培养解决实际问题的能力。这种教学方法有助于提高学生的动手能力和创新思维。

特征2：技术工具整合应用 | 教学中使用了Python代码演示、Run和Run Model命令运行流程解析等技术工具，帮助学生直观理解编程过程。这种教学方式提升了学生的技术应用能力，...

**检索结果 3** (融合分数: 0.185)
文档ID: 82f078848b5840a49938cd0344d8ae68_teaching_modes
检索方法: KNN
内容: 教学模式1: 任务驱动法, 0.9, 教学摘要中明确提到采用任务驱动法，通过项目方案书、程序开发、小组协作任务等具体任务引导学生学习，强调实践和问题解决。
教学模式2: 项目式学习, 0.8, 教学摘要中提到以智能生产线项目为切入点，通过项目方案书和实物演示等方式，让学生在真实项目中学习和应用知识。
教学模式3: 合作学习, 0.7, 教学摘要中提到小组协作任务，要求学生基于"Parent台座"功...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}