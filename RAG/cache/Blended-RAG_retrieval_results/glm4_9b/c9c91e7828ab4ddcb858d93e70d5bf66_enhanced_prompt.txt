分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: c9c91e7828ab4ddcb858d93e70d5bf66
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.195)
文档ID: c9c91e7828ab4ddcb858d93e70d5bf66_keywords
检索方法: KNN
内容: 互动教学, 多媒体技术, 花钟主题, 游戏化设计, 文本解读, 语言实践, 角色扮演, 科学原理

**检索结果 2** (融合分数: 0.169)
文档ID: c9c91e7828ab4ddcb858d93e70d5bf66_teaching_modes
检索方法: KNN
内容: 教学模式1: 多媒体互动教学, 置信度: 0.9, 识别依据：摘要中多次提到使用多媒体技术（PPT、多屏互动投影、手机互动投影）和互动教学（游戏、角色扮演、配乐朗读），强调技术手段与教学活动的深度融合。  
教学模式2: 游戏化教学, 置信度: 0.8, 识别依据：摘要明确提及多个游戏环节（"看诗句找花名"、"火车火车哪里开"），并通过游戏化设计促进学生参与和认知激活。  
教学模式3: 混合式学...

**检索结果 3** (融合分数: 0.162)
文档ID: c9c91e7828ab4ddcb858d93e70d5bf66_subject_features
检索方法: KNN
内容: 学科：语文（小学高年级）

特征1：多媒体与互动技术融合 | 教学价值：通过PPT、多屏互动投影、手机互动投影等技术手段，将抽象的文字知识与直观的视觉、听觉信息相结合，增强学生的学习兴趣和参与度，同时提高课堂效率，使知识传递更生动形象。

特征2：游戏化教学设计 | 教学价值：通过"看诗句找花名"、"火车火车哪里开"等游戏，激发学生的好奇心和竞争意识，使学生在轻松愉快的氛围中学习，同时培养团队合作...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}