分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 93eef61642a94e35b1e1c92962aa5630
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.225)
文档ID: 93eef61642a94e35b1e1c92962aa5630_keywords
检索方法: KNN
内容: 任务驱动, 文化教学, 信息技术, 思维导图, 口语练习, 时间表达, 互动教学, 情境导入

**检索结果 2** (融合分数: 0.215)
文档ID: 93eef61642a94e35b1e1c92962aa5630_subject_features
检索方法: KNN
内容: 学科：英语（语言学习）

特征1：描述 | 教学价值  
描述：本节课通过情境导入、任务驱动和文化拓展实现语言与文化的双重教学目标，强调在实际语境中学习和运用语言。教师利用节日视频、电子词匾、EN5文字功能等工具展示核心词汇，并通过PPT推送语段任务、思维导图复述等活动强化语言输入和输出。  
教学价值：帮助学生将语言学习与文化背景相结合，提高语言的实际应用能力，同时增强文化意识。

特征2：描述...

**检索结果 3** (融合分数: 0.190)
文档ID: 93eef61642a94e35b1e1c92962aa5630_teaching_modes
检索方法: KNN
内容: 教学模式1: 任务驱动教学法, 0.9, 识别依据：摘要中明确提到“任务驱动”和“PPT推送语段任务”，学生通过完成具体任务（如复述庆祝计划、讨论春节计划）进行学习，教师也通过任务引导学生表达和个人意愿。
教学模式2: 技术增强型教学, 0.8, 识别依据：摘要多次提及使用电子词匾、EN5文字功能、PPT、1N5图片插入功能等技术工具，这些工具直接支持教学内容和学生互动。
教学模式3: 文化沉浸式...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}