分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 8fd2b012c9fc48fdb2f03ae3b2f488d0
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.198)
文档ID: 8fd2b012c9fc48fdb2f03ae3b2f488d0_teaching_modes
检索方法: KNN
内容: 教学模式1: 混合式学习, 0.9, 识别依据：摘要中明确提到结合VR虚拟现实、实物观察、多媒体交互等多种技术手段进行教学，同时融合了讲述法、讨论法、小组合作等多种教学方法，体现了混合式学习的特征。
教学模式2: 探究式学习, 0.8, 识别依据：教师在教学中通过设置问题（如“起义前后”“为何选择井冈山”），引导学生自主探究和思考，并通过小组讨论等形式促进学生主动构建知识。
教学模式3: 合作学习...

**检索结果 2** (融合分数: 0.194)
文档ID: 8fd2b012c9fc48fdb2f03ae3b2f488d0_subject_features
检索方法: KNN
内容: 学科：历史

特征1：情境创设与技术融合 | 教学价值：通过VR虚拟现实、多媒体交互等技术手段，将学生带入南昌起义纪念馆等历史场景，增强学习的沉浸感和直观性，使抽象的历史事件变得生动可感，激发学生的学习兴趣和情感共鸣。

特征2：史料分析与问题驱动 | 教学价值：通过触摸屏观察军旗纹样、放大镜分析油画细节、地图定位、阅读材料分析等环节，引导学生从具体史料中提取信息，结合问题如"起义前后""为何选择...

**检索结果 3** (融合分数: 0.161)
文档ID: 8fd2b012c9fc48fdb2f03ae3b2f488d0_keywords
检索方法: KNN
内容: VR虚拟现实, 多媒体交互, 历史认知, 战略决策, 小组讨论, 直观感受, 思维训练, 井冈山精神

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}