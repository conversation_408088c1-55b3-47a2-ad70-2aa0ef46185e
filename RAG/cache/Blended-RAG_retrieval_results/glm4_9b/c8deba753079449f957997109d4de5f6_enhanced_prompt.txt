分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: c8deba753079449f957997109d4de5f6
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.226)
文档ID: c8deba753079449f957997109d4de5f6_keywords
检索方法: KNN
内容: 感恩节, 中秋节, 小组合作, 任务驱动, 词汇教学, 文化对比, 深度学习, 多媒体工具

**检索结果 2** (融合分数: 0.183)
文档ID: c8deba753079449f957997109d4de5f6_subject_features
检索方法: KNN
内容: 学科：英语（综合素养课程）

特征1：跨文化对比教学 | 通过对比感恩节与中华传统节日的异同，培养学生的跨文化意识和理解能力，帮助学生建立文化包容性。

特征2：任务驱动与小组合作 | 以"制作火鸡餐"等任务为驱动，通过小组合作学习，提高学生的团队协作能力和自主学习能力。

特征3：情境化与多媒体辅助教学 | 利用PPT、视频等多媒体工具创设真实语境，增强教学的直观性和趣味性，帮助学生更好地理解和...

**检索结果 3** (融合分数: 0.169)
文档ID: c8deba753079449f957997109d4de5f6_teaching_modes
检索方法: KNN
内容: 教学模式1: 任务驱动模式, 0.9, 教学摘要中明确提到"任务驱动模式"，并通过具体任务（如填空练习、制作火鸡餐讨论、角色扮演等）推动教学进程，强调学生通过完成任务来学习和应用知识。
教学模式2: 小组合作模式, 0.8, 摘要中多次提及"小组合作"，如"小组合作阶段，学生围绕'如何制作火鸡餐'展开讨论"，以及通过"分组竞赛"等活动促进学生协作，体现了以小组为单位共同解决问题的特点。
教学模式3...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}