分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: e2f2f3e1fcde439cbb688a399462fbfb
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.200)
文档ID: e2f2f3e1fcde439cbb688a399462fbfb_teaching_modes
检索方法: KNN
内容: 教学模式1: 任务驱动教学法, 0.85, 识别依据：摘要中多次提到通过具体任务（如角色扮演、手工制作、时间表达训练）引导学生学习，强调学生主动参与和完成任务的过程。
教学模式2: 情境教学法, 0.80, 识别依据：摘要描述了多种情境创设方法（如卡通视频、角色扮演“午餐时间”），通过模拟真实生活场景帮助学生理解和应用知识。
教学模式3: 技术增强教学法, 0.75, 识别依据：摘要明确提到了多种...

**检索结果 2** (融合分数: 0.158)
文档ID: e2f2f3e1fcde439cbb688a399462fbfb_keywords
检索方法: KNN
内容: 教师节, 角色扮演, 时间表达, 文化差异, 技术工具, 任务驱动, 情感教育, 语言训练

**检索结果 3** (融合分数: 0.138)
文档ID: e2f2f3e1fcde439cbb688a399462fbfb_subject_features
检索方法: KNN
内容: 学科：英语

特征1：描述 | 教学价值  
描述：本节课以语言技能训练为核心，通过歌曲导入、角色扮演、实物钟表模型互动等方式，强化词汇认读、语法运用和口语表达。例如，通过《祝你生日快乐》复习基数词，利用拟声词角色扮演激活语言感知。  
教学价值：提升学生的语言输入与输出能力，培养跨文化交际意识，增强课堂互动性，促进语言知识的实际应用。

特征2：描述 | 教学价值  
描述：课程融合多媒体技术（...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}