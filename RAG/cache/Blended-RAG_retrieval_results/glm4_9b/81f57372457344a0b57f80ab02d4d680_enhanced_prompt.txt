分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 81f57372457344a0b57f80ab02d4d680
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.229)
文档ID: 81f57372457344a0b57f80ab02d4d680_keywords
检索方法: KNN
内容: 二次函数, 参数影响, 动态演示, 平移规律, 探究学习, 性质分析, 规范书写, 认知闭环

**检索结果 2** (融合分数: 0.214)
文档ID: 81f57372457344a0b57f80ab02d4d680_teaching_modes
检索方法: KNN
内容: 教学模式1: 0.85, 教学摘要中体现了"探究式学习"模式，通过"提问—探究—验证"的方式引导学生主动发现和归纳数学规律，如平移规律和函数变换与图像变化的联系。
教学模式2: 0.80, 教学摘要中采用了"建构主义"模式，通过学生实践操作（绘制函数图像）、小组讨论和教师引导，帮助学生逐步构建对二次函数性质的理解。
教学模式3: 0.75, 教学摘要中包含了"发现式学习"模式，通过动态演示和可视化...

**检索结果 3** (融合分数: 0.204)
文档ID: 81f57372457344a0b57f80ab02d4d680_subject_features
检索方法: KNN
内容: 学科：数学  
特征1：描述 | 教学价值  
- 描述：本节课以二次函数图像性质为核心，通过"验证→观察→推导→应用"的逻辑链推进教学，强调数学概念的形成过程和逻辑推理。教师利用希沃白板和Desmos动态演示工具，将抽象的数学公式与直观的图形结合，帮助学生理解参数a、h、k对图像的影响。  
- 教学价值：这种教学模式有助于培养学生的逻辑思维能力和空间想象能力，通过可视化手段降低抽象概念的认知难...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}