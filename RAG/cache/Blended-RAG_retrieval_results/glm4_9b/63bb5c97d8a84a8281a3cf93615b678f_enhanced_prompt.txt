分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 63bb5c97d8a84a8281a3cf93615b678f
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.259)
文档ID: 63bb5c97d8a84a8281a3cf93615b678f_keywords
检索方法: KNN
内容: 算法, 人工智能, 跨学科, 游戏探究, 案例分析, 技术手段, 规则测试, 教学模式

**检索结果 2** (融合分数: 0.191)
文档ID: 63bb5c97d8a84a8281a3cf93615b678f_subject_features
检索方法: KNN
内容: 学科：计算机科学与技术（人工智能方向）

特征1：算法思维培养 | 通过游戏探究（如"猜画小歌"）、案例分析（垃圾分类）和流程图解析（贪心算法与决策树），引导学生理解算法作为解决问题的逻辑步骤，培养抽象思维和系统化分析能力。  
教学价值：强化学生对算法本质的认知，为后续编程学习奠定基础。

特征2：跨学科融合实践 | 结合环保场景设计任务，将算法应用于真实生活情境，体现计算机科学与其他学科的交叉...

**检索结果 3** (融合分数: 0.175)
文档ID: 63bb5c97d8a84a8281a3cf93615b678f_teaching_modes
检索方法: KNN
内容: 教学模式1: 建构主义, 0.9, 识别依据：教学过程中强调学生的主动参与和探究，通过游戏、案例分析、跨学科实践等方式引导学生自主构建知识体系，如"猜画小歌"小程序游戏、"垃圾分类场景"设计等。
教学模式2: 技术增强型学习, 0.8, 识别依据：教学过程中大量运用西沃白板、易课堂、平板交互等技术手段，如"平板上传决策树模型"、"屏幕共享"等，技术手段与教学内容紧密结合，提升学习效果。
教学模式3...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}