分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 837e7d7ea96e44febb158737c22f206b
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.269)
文档ID: 837e7d7ea96e44febb158737c22f206b_keywords
检索方法: KNN
内容: 数与形, 分数运算, 动态演示, 交互式教学, 奇数规律, 平方数本质, 知识迁移, 抽象概括

**检索结果 2** (融合分数: 0.219)
文档ID: 837e7d7ea96e44febb158737c22f206b_subject_features
检索方法: KNN
内容: 学科：数学

特征1：数形结合的直观教学 | 通过图形（如正方形面积）和数值（平方数、奇数序列）的结合，将抽象的数学概念转化为可视化形式，帮助学生建立直观理解，降低认知难度，提升学习兴趣。

特征2：探究式学习设计 | 教师通过动态演示、拖拽操作任务等引导学生自主探究规律（如"图形面积=平方数=奇数和"），培养学生的学习主动性和问题解决能力，促进高阶思维发展。

特征3：分层递进的认知引导 | 采...

**检索结果 3** (融合分数: 0.183)
文档ID: 837e7d7ea96e44febb158737c22f206b_teaching_modes
检索方法: KNN
内容: 教学模式1: 探究式学习, 置信度: 0.9, 识别依据: 教学过程中强调引导学生通过"拖拽操作任务"、"发现规律"、"归纳总结"等方式自主探究知识，体现了探究式学习的核心特征。
教学模式2: 合作学习, 置信度: 0.8, 识别依据: 教学设计中提到"分组操作、讨论探究"等策略，表明学生在小组中合作完成任务，符合合作学习的模式。
教学模式3: 技术增强型教学, 置信度: 0.85, 识别依据: ...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}