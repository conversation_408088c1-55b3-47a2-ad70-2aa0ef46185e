分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 39fc1b0cecbb4076a31d3f5cb24181f9
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.235)
文档ID: 39fc1b0cecbb4076a31d3f5cb24181f9_keywords
检索方法: KNN
内容: 游戏化教学, 兴趣爱好, 句型训练, 第三人称单数, 角色扮演, 小组互访, 技术融合, 认知梯度

**检索结果 2** (融合分数: 0.190)
文档ID: 39fc1b0cecbb4076a31d3f5cb24181f9_subject_features
检索方法: KNN
内容: 学科：英语

特征1：游戏化教学策略 | 通过游戏（如"选择学生"、"猜爱好"）激发学生学习兴趣，增强课堂互动性，使语言学习过程更生动有趣，提高参与度。
特征2：句型输入与输出训练 | 教师通过示范句型（如"like to swing/climb"）进行输入，再通过"选择-描述-验证"等步骤引导学生输出，强化句型掌握，培养实际语言运用能力。
特征3：第三人称单数变化规则教学 | 结合具体动作（"噔...

**检索结果 3** (融合分数: 0.178)
文档ID: 39fc1b0cecbb4076a31d3f5cb24181f9_teaching_modes
检索方法: KNN
内容: 教学模式1: 游戏化教学 (0.9), 识别依据：摘要中多次提到游戏化策略，如"选择学生"活动、"猜爱好"游戏、击掌鼓掌等肢体互动，以及使用班级优化大师进行随机点评和积分制维持节奏，这些都体现了以游戏为驱动力的教学模式。

教学模式2: 合作学习 (0.8), 识别依据：摘要中提到"小组互访"和"填空练习"等任务，这些活动需要学生分组合作完成，体现了合作学习的特征。

教学模式3: 分层教学 (0...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}