分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: a1e101340204460ab23e8c14706133a0
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.274)
文档ID: a1e101340204460ab23e8c14706133a0_keywords
检索方法: KNN
内容: 电路分析, 电学计算, 问题链引导, 数据可视化, 典型解析, 串联电路, 电压表读数, 师生互动

**检索结果 2** (融合分数: 0.215)
文档ID: a1e101340204460ab23e8c14706133a0_subject_features
检索方法: KNN
内容: 学科：物理（电路分析与电学计算）

特征1：数据分析驱动教学 | 通过好分数平台和HiTeach平台的数据分析，教师精准定位学生薄弱环节，实现针对性教学，提高教学效率。

特征2：实验与理论结合 | 教师利用PPT动态演示和实物电路板操作相结合，帮助学生直观理解抽象的电路概念，增强理论与实践的联系。

特征3：问题导向的教学方法 | 采用"问题归类—典型解析—总结归纳—举一反三"四步法和"观察数据...

**检索结果 3** (融合分数: 0.209)
文档ID: a1e101340204460ab23e8c14706133a0_teaching_modes
检索方法: KNN
内容: 教学模式1: 建构主义, 0.9, 识别依据：教师通过问题链引导学生逐步深入，采用"问题归类—典型解析—总结归纳—举一反三"四步法和"观察数据差异—提出质疑—引导推理—验证结论"阶梯式提问，强调学生的主动探究和意义建构。同时，通过"肯定+追问"策略强化学生思维，促进知识的内化。
教学模式2: 交互式教学, 0.8, 识别依据：课堂中存在大量的师生互动环节，如邀请学生现场演示、通过追问突破认知误区、...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}