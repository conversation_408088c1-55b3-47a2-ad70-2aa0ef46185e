分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: fdd545c17f174815928fefab46894dc3
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.215)
文档ID: fdd545c17f174815928fefab46894dc3_subject_features
检索方法: KNN
内容: 学科：数学  
特征1：函数图像与性质的探究式教学 | 通过"描点法"和参数动态演示，引导学生自主发现函数图像特征及参数影响，培养数形结合能力。  
特征2：技术融合的混合式教学 | 利用乐客平台、电子白板等工具实现线上线下协同学习，提升教学效率和互动性。  
特征3：分层差异化指导 | 教师根据学生表现调整教学节奏和难度，确保所有学生都能在原有基础上进步。  
特征4：可视化辅助记忆 | 结合P...

**检索结果 2** (融合分数: 0.197)
文档ID: fdd545c17f174815928fefab46894dc3_keywords
检索方法: KNN
内容: 一次函数, 线上线下融合, 探究活动, 参数影响, 小组协作, 动态演示, 即时练习, 分层指导

**检索结果 3** (融合分数: 0.194)
文档ID: fdd545c17f174815928fefab46894dc3_teaching_modes
检索方法: KNN
内容: 教学模式1: 建构主义, 0.9, 识别依据：摘要中强调通过"探究活动"、"小组协作"、"画图-观察-归纳"等方式引导学生自主构建知识体系，符合建构主义的核心思想。
教学模式2: 互动式教学, 0.8, 识别依据：摘要中提到教师运用平台功能组织学生讨论、对比、提问，并通过视觉辅助强化记忆，体现了师生、生生之间的多向互动。
教学模式3: 分层教学, 0.7, 识别依据：摘要明确指出教师进行"分层指导...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}