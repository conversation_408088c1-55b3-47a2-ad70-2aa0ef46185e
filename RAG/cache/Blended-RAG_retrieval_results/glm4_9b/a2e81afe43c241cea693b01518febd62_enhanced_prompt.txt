分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: a2e81afe43c241cea693b01518febd62
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.245)
文档ID: a2e81afe43c241cea693b01518febd62_keywords
检索方法: KNN
内容: 多媒体教学, 动词教学, 汉字文化, 角色扮演, 课堂互动, 教学模式, 字形规律, 实践训练

**检索结果 2** (融合分数: 0.200)
文档ID: a2e81afe43c241cea693b01518febd62_subject_features
检索方法: KNN
内容: 学科：语文（小学阶段）

特征1：多媒体资源整合运用 | 教学价值：通过智慧课堂平板、PPT、视频等现代化教学手段，将抽象的文字学习与直观的视觉、听觉刺激相结合，增强学生的学习兴趣和参与度，同时帮助学生更清晰地理解和记忆故事内容和汉字结构。

特征2：情境化教学设计 | 教学价值：以《小猴子下山》的故事为情境主线，通过角色扮演、动作演绎等活动，让学生在具体情境中学习和运用动词，提高语言表达能力和情...

**检索结果 3** (融合分数: 0.175)
文档ID: a2e81afe43c241cea693b01518febd62_teaching_modes
检索方法: KNN
内容: 教学模式1: 0.85, 识别依据：摘要中强调通过多媒体资源（智慧课堂平板、PPT、视频）构建教学流程，结合情境导入、游戏、角色扮演等活动，体现了技术增强型教学模式和活动探究式教学模式的特点。
教学模式2: 0.75, 识别依据：摘要中描述了"提问-讨论-示范-实践"的教学模式，以及学生分组完成任务、互评等互动环节，符合合作学习教学模式。
教学模式3: 0.65, 识别依据：摘要中通过汉字书写教学...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}