分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: c18332f4a539460db10100fccfc8bb20
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.280)
文档ID: c18332f4a539460db10100fccfc8bb20_keywords
检索方法: KNN
内容: 物联网, 项目式学习, 实践操作, 硬件连接, 编程逻辑, 云平台, 互动教学, 教学模式

**检索结果 2** (融合分数: 0.211)
文档ID: c18332f4a539460db10100fccfc8bb20_teaching_modes
检索方法: KNN
内容: 教学模式1: 项目式学习 (Project-Based Learning), 置信度: 0.95, 识别依据: 教学摘要明确提到"项目式学习"，并通过具体的项目（如智能硬件与编程逻辑的探索）引导学生自主探究，强调解决实际问题的过程。
教学模式2: 演示+实操 (Demonstration + Hands-on Practice), 置信度: 0.90, 识别依据: 教学过程中包含教师的演示（如魔术...

**检索结果 3** (融合分数: 0.190)
文档ID: c18332f4a539460db10100fccfc8bb20_subject_features
检索方法: KNN
内容: 学科：信息技术（物联网技术方向）

特征1：项目式学习 | 通过设计驱动的问题解决和实践活动，让学生在真实情境中构建知识，培养创新思维和协作能力。这种教学模式有助于学生将理论知识应用于实际操作，增强学习的实用性和趣味性。

特征2：理论与实践结合 | 课堂中既包含理论讲解（如物联网核心要素），也涵盖动手操作（如硬件连接和编程），使学生能够全面理解技术原理并掌握实践技能。这种结合有助于加深学生对知识...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}