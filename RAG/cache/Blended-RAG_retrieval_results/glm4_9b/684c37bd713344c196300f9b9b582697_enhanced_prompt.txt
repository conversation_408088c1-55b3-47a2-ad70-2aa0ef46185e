分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 684c37bd713344c196300f9b9b582697
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.251)
文档ID: 684c37bd713344c196300f9b9b582697_keywords
检索方法: KNN
内容: 游戏化教学, 任务驱动, 国家地理, 语言学习, 技术工具, 角色扮演, 差异化指导, 探究式学习

**检索结果 2** (融合分数: 0.225)
文档ID: 684c37bd713344c196300f9b9b582697_teaching_modes
检索方法: KNN
内容: 教学模式1: 探究式学习, 0.9, 识别依据：摘要中强调学生通过观察、讨论、角色扮演等方式主动获取知识，结合技术工具（AI地球仪、AR地图）和任务（"传递卡片"、"Who can help?"）推动学习过程，符合探究式学习的核心特征。
教学模式2: 任务驱动学习, 0.8, 识别依据：摘要明确提到采用"任务驱动模式"，如"点它吧"、"Bingo"单词记忆任务、"传递卡片"空间认知任务等，这些任务...

**检索结果 3** (融合分数: 0.176)
文档ID: 684c37bd713344c196300f9b9b582697_subject_features
检索方法: KNN
内容: 学科：地理与语言学习（双语教学）

特征1：游戏化与任务驱动教学 | 通过"走！走！走！"、"点它吧"、"Bingo"等游戏化活动，结合"传递卡片"、"Body Girl"等趣味任务，激发学生学习兴趣，提高课堂参与度，同时通过任务完成强化知识记忆和应用能力。

教学价值：增强学习的趣味性和互动性，降低学习焦虑，提升学生的主动性和合作意识，使语言和地理知识在轻松愉快的氛围中习得。

特征2：技术工具...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}