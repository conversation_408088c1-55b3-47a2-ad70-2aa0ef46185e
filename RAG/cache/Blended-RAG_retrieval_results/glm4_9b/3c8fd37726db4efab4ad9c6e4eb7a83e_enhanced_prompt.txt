分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 3c8fd37726db4efab4ad9c6e4eb7a83e
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.229)
文档ID: 3c8fd37726db4efab4ad9c6e4eb7a83e_keywords
检索方法: KNN
内容: 中国共产党的奋斗历程,VR教学,历史思维,合作探究,情感共鸣,知识重构,现代手段,情境学习

**检索结果 2** (融合分数: 0.193)
文档ID: 3c8fd37726db4efab4ad9c6e4eb7a83e_subject_features
检索方法: KNN
内容: 学科：思想政治（或道德与法治）

特征1：情境化教学 | 通过VR技术和数字游戏创设虚拟历史场景，增强学生对历史事件的直观感受，提高学习的沉浸感和参与度，有助于学生更深刻地理解抽象的政治概念和历史进程。

特征2：史料实证 | 运用历史照片与史料实证任务，引导学生进行史料分析，培养学生的批判性思维和时空观念，帮助他们基于证据形成历史解释和政治判断。

特征3：合作探究 | 学生分组合作梳理百年党史...

**检索结果 3** (融合分数: 0.173)
文档ID: 3c8fd37726db4efab4ad9c6e4eb7a83e_teaching_modes
检索方法: KNN
内容: 教学模式1: 混合式学习, 0.9, 识别依据：该教学摘要中明确提到了多种现代教育技术的应用，如720云全景VR软件、西沃拖拽小游戏等，同时结合了传统的教学方法如师生问答互动和史料实证任务，体现了线上线下、技术与传统相结合的混合式学习特征。

教学模式2: 情境式学习, 0.8, 识别依据：摘要中多次提到通过虚拟场景（如智能化思政教学中心展馆）和具体案例（如抗疫党员事迹）来创设情境，让学生在情境中...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}