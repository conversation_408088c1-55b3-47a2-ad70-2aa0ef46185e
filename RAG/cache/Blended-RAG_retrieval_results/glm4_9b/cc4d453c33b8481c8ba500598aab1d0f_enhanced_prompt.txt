分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: cc4d453c33b8481c8ba500598aab1d0f
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.297)
文档ID: cc4d453c33b8481c8ba500598aab1d0f_keywords
检索方法: KNN
内容: 键盘操作, 指法训练, 多媒体教学, 互动游戏, 基准键定位, 教师示范, 学生模仿, 小组互评

**检索结果 2** (融合分数: 0.209)
文档ID: cc4d453c33b8481c8ba500598aab1d0f_subject_features
检索方法: KNN
内容: 学科：计算机基础/信息技术  

特征1：实践操作性 | 教学价值：本节课强调键盘操作的实践训练，通过"教师示范+学生模仿"和互动游戏等方式，让学生在动手操作中掌握指法技巧。这种教学模式有助于培养学生的实际操作能力，符合计算机技能学习的规律。  

特征2：多媒体辅助教学 | 教学价值：利用希沃白板、视频动画、手机直播等技术手段，将抽象的键盘知识可视化、动态化，增强教学的直观性和趣味性。多媒体资源...

**检索结果 3** (融合分数: 0.204)
文档ID: cc4d453c33b8481c8ba500598aab1d0f_teaching_modes
检索方法: KNN
内容: 教学模式1: 0.85, 识别依据：摘要中强调了"教师示范+学生模仿"模式，结合多媒体资源（希沃白板、视频动画）进行直观教学，并通过互动游戏、小组互评等方式促进学生主动参与，体现了以教师引导为主、学生实践为辅的教学特点，符合建构主义教学模式。
教学模式2: 0.75, 识别依据：摘要中提到通过系统讲解、分步演示（如"手指归位-基准键锁定-指尖敲击"三步法）、口诀记忆（如"敲完即回基准键"）等策略，...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}