分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: a5780baa677c4934b23ad9ccef0953af
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.222)
文档ID: a5780baa677c4934b23ad9ccef0953af_teaching_modes
检索方法: KNN
内容: 教学模式1: 0.85, 该摘要体现了混合式学习模式，结合了线上直播（钉钉）和线下互动（白板工具），并通过多种技术手段（PPT、连麦、群组反馈）实现教学过程的融合与协同。
教学模式2: 0.75, 该摘要展示了探究式学习模式，通过情境探究（六一采购气球）、实物计数、分层提问等环节，引导学生主动推导和建构知识。
教学模式3: 0.65, 该摘要反映了个性化学习模式，通过分层练习、即时奖励、多种解题策...

**检索结果 2** (融合分数: 0.214)
文档ID: a5780baa677c4934b23ad9ccef0953af_keywords
检索方法: KNN
内容: 整十整百数, 加减法, 钉钉直播, 白板工具, 分层练习, 情境探究, 数位分解, 多人操作

**检索结果 3** (融合分数: 0.200)
文档ID: a5780baa677c4934b23ad9ccef0953af_subject_features
检索方法: KNN
内容: 学科：数学  
特征1：描述 | 教学价值  
- 描述：本节课以整十整百数的加减法为核心内容，通过具体的数学运算训练，结合数位分解等数学方法，帮助学生理解和掌握基本的数学运算规律。  
- 教学价值：强化学生的基础数学运算能力，培养其逻辑思维和问题解决能力，为后续更复杂的数学学习打下坚实基础。  

特征2：描述 | 教学价值  
- 描述：课程采用“六一采购气球”等生活化情境，将抽象的数学知识...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}