分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: b482cd3cb0dd439f8abf6c350789ec9c
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.235)
文档ID: b482cd3cb0dd439f8abf6c350789ec9c_teaching_modes
检索方法: KNN
内容: 教学模式1: 启发式教学法, 0.85, 教师通过动态对比、即时作答系统、思维导图等方式引导学生自主发现规律，强调学生主动探究和概念建构。
教学模式2: 合作学习模式, 0.75, 学生分组讨论，教师通过对比分析引导学生修正答案，肯定逻辑严谨性，体现小组互动与协作。
教学模式3: 技术增强型教学模式, 0.80, 教师运用多媒体、即时作答系统等技术手段，结合积分机制、拆分验证法等提升参与度和效率。

**检索结果 2** (融合分数: 0.234)
文档ID: b482cd3cb0dd439f8abf6c350789ec9c_keywords
检索方法: KNN
内容: 多项式因式分解, 探究式教学, 小组讨论, 启发式提问, 技术赋能, 核心素养, 概念建构, 拆分验证法

**检索结果 3** (融合分数: 0.194)
文档ID: b482cd3cb0dd439f8abf6c350789ec9c_subject_features
检索方法: KNN
内容: 学科：数学（代数）

特征1：代数运算与逆运算关系的探究 | 教学价值：通过对比整式乘积与多项式和的逆运算关系，帮助学生理解代数结构中的逻辑联系，培养逆向思维能力，为后续复杂运算打下基础。

特征2：动态化与可视化教学手段的应用 | 教学价值：利用PPT动态对比和即时作答系统，将抽象的代数概念具象化，增强学生的直观感受，提高学习兴趣和效率。

特征3：结构化知识框架的构建 | 教学价值：通过思维导...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}