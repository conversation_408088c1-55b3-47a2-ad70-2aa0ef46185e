分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: d86366ba95dd43f6a0e19abb7f62133d
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.211)
文档ID: d86366ba95dd43f6a0e19abb7f62133d_subject_features
检索方法: KNN
内容: 学科：数学（或科学）

特征1：描述 | 教学价值  
描述：本节课以“周期现象”为核心概念，通过生活实例（如微视频、气球压强现象）和数学问题（如日历推算春节星期）引入和探究周期现象，强调概念的抽象性和应用性。教学价值在于帮助学生建立对周期现象的直观认识和数学表达，培养从具体到抽象的思维过渡能力。  

特征2：描述 | 教学价值  
描述：采用线上线下融合模式，利用iSchool、安思护、诺比有...

**检索结果 2** (融合分数: 0.201)
文档ID: d86366ba95dd43f6a0e19abb7f62133d_teaching_modes
检索方法: KNN
内容: 教学模式1: 建构主义, 0.9, 识别依据：摘要中强调学生的自主学习和探究，通过分析周期现象的特征和解决实际问题来构建知识，体现了建构主义的核心思想，即知识是通过个体主动参与和体验而形成的。
教学模式2: 混合式学习, 0.8, 识别依据：摘要明确提到采用线上线下融合模式，利用多种技术平台（iSchool、安思护、诺比有声云平台）进行教学，这符合混合式学习的定义，即结合线上和线下资源进行教学活动...

**检索结果 3** (融合分数: 0.189)
文档ID: d86366ba95dd43f6a0e19abb7f62133d_keywords
检索方法: KNN
内容: 周期现象, 线上线下融合, 分层探究, 数据反馈, 情境导入, 概念建构, 互动答题, 认知发展

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}