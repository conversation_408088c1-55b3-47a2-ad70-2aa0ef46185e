分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: f8120412786b48a7bd4dac77d255f35e
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.173)
文档ID: f8120412786b48a7bd4dac77d255f35e_subject_features
检索方法: KNN
内容: 学科：英语（语言学习）

特征1：游戏化设计与多媒体辅助 | 通过游戏和多媒体资源（如视频、PPT、音频）激发学生学习兴趣，增强学习的趣味性和互动性，使语言输入更加生动直观，有助于提高学生的参与度和学习效果。

特征2：分层评价与即时反馈 | 采用分层评价系统（自评、互评、师评）和电子评价工具，实现个性化学习支持和即时反馈，帮助学生及时调整学习策略，同时促进教师对教学过程的动态调控。

特征3：情...

**检索结果 2** (融合分数: 0.157)
文档ID: f8120412786b48a7bd4dac77d255f35e_teaching_modes
检索方法: KNN
内容: 教学模式1: 0.85, 识别依据：该教学摘要体现了"建构主义学习理论"，通过游戏化设计、多媒体辅助和分层评价等手段，引导学生主动探索和构建知识体系。例如，"FUNTIME"和"CartoonTime"环节通过游戏和动画激发学生兴趣，促进认知激活和语言输入，而"SunTime"环节的自主上传和同伴互评则体现了学生主体性和知识建构的过程。

教学模式2: 0.75, 识别依据：该教学摘要体现了"情境...

**检索结果 3** (融合分数: 0.152)
文档ID: f8120412786b48a7bd4dac77d255f35e_keywords
检索方法: KNN
内容: 水果,游戏化,多媒体,分层评价,阅读评价,同伴互评,角色扮演,启发式提问

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}