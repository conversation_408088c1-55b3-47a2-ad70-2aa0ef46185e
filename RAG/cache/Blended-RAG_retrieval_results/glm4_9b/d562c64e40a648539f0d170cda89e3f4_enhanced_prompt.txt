分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: d562c64e40a648539f0d170cda89e3f4
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.194)
文档ID: d562c64e40a648539f0d170cda89e3f4_teaching_modes
检索方法: KNN
内容: 教学模式1: 任务驱动教学法, 0.85, 识别依据：摘要中明确提到“任务驱动法”和“限时心理描写等任务”，以及“小组对抗机制提升参与度”，这些都是典型的任务驱动教学法的特征。
教学模式2: 情境模拟教学法, 0.75, 识别依据：摘要中提到“通过电影片段引发情感共鸣”和“情境模拟”，这些表明教师通过创设情境来帮助学生理解和体验情感描写。
教学模式3: 技术整合教学法, 0.80, 识别依据：摘要...

**检索结果 2** (融合分数: 0.173)
文档ID: d562c64e40a648539f0d170cda89e3f4_subject_features
检索方法: KNN
内容: 学科：语文

特征1：情感描写为核心目标 | 教学价值：聚焦语文核心素养中的"语言建构与运用"，通过多维表现手法的解析（如《荷马史诗》《伊芝玛姑娘》等案例），培养学生对情感描写的敏感性和表达能力，强化文学鉴赏能力。

特征2：技术整合与互动设计 | 教学价值：利用Xiwo白板平台、倒计时功能及"超级链接"技术，将抽象的文字描述转化为可交互任务，突破传统单向灌输模式，提升课堂参与度和学习效率。

特...

**检索结果 3** (融合分数: 0.151)
文档ID: d562c64e40a648539f0d170cda89e3f4_keywords
检索方法: KNN
内容: 情感描写, 任务驱动, 信息技术, 小组对抗, 追问式提问, 视觉感知, 圈画观察, 影视案例

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}