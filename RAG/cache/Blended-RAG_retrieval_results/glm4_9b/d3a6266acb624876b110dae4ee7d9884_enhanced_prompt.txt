分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: d3a6266acb624876b110dae4ee7d9884
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.215)
文档ID: d3a6266acb624876b110dae4ee7d9884_teaching_modes
检索方法: KNN
内容: 教学模式1: 探究式学习, 0.85, 识别依据：摘要中多次提到“探究式学习”，如“学生通过点击工具图标完成考古任务”、“学生以导游身份阐释文化内涵”、“学生通过自主搜索完成知识建构”，强调学生主动探索和发现知识的过程。
教学模式2: 多媒体辅助教学, 0.80, 识别依据：摘要中大量提及多种多媒体技术手段，如“Animate动画”、“荣耀平板”、“VR全景技术”、“SWF动画”、“思维导图”、“...

**检索结果 2** (融合分数: 0.215)
文档ID: d3a6266acb624876b110dae4ee7d9884_keywords
检索方法: KNN
内容: 探究式学习,VR全景,情境创设,技术融合,文化认同,历史认知,小组协作,数字资源

**检索结果 3** (融合分数: 0.182)
文档ID: d3a6266acb624876b110dae4ee7d9884_subject_features
检索方法: KNN
内容: 学科：历史

特征1：情境创设与问题驱动 | 通过"考古盲盒"、虚拟考古模拟等情境设计激发学生兴趣，以问题链引导探究，使历史学习更具互动性和启发性，培养问题解决能力。
特征2：技术融合与沉浸式体验 | 运用Animate动画、VR全景、故宫数字资源等技术手段，创造沉浸式学习环境，增强学生对历史文化的直观感受和理解深度。
特征3：跨学科认知框架构建 | 结合科技工具（如SWF动画、思维导图）整合天文...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}