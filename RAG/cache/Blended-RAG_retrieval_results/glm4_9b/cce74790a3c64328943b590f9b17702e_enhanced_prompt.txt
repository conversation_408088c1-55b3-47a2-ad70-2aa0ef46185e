分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: cce74790a3c64328943b590f9b17702e
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.196)
文档ID: cce74790a3c64328943b590f9b17702e_subject_features
检索方法: KNN
内容: 学科：跨学科融合（主要涉及信息技术、物理、特殊教育）

特征1：项目式学习（PBL） | 教学价值：通过"手语翻译手套"项目驱动学习，增强学生的实践能力和问题解决能力，将理论知识与实际应用相结合，提升学习动机和参与度。

特征2：多模态教学支持 | 描述：利用交互式电子白板播放视频、放大动作、对比颜色等手段，结合触觉感知活动，多感官呈现教学内容，帮助学生更直观地理解复杂概念。  
教学价值：提高教...

**检索结果 2** (融合分数: 0.196)
文档ID: cce74790a3c64328943b590f9b17702e_teaching_modes
检索方法: KNN
内容: 教学模式1: 项目式学习 (Project-Based Learning), 置信度: 0.95, 识别依据: 教学摘要明确指出采用项目式教学模式，围绕手语翻译手套项目展开，包含完整的项目周期（问题引入、知识分析、技术实践、成果展示），强调学生通过实际项目构建知识体系。
教学模式2: 探究式学习 (Inquiry-Based Learning), 置信度: 0.85, 识别依据: 教师通过提问（如...

**检索结果 3** (融合分数: 0.171)
文档ID: cce74790a3c64328943b590f9b17702e_keywords
检索方法: KNN
内容: 项目式教学, 手语翻译, 传感器技术, 弯曲度传感, 电子白板, 触觉感知, Arduino套件, 问题链教学

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}