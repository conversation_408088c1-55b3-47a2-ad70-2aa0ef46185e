分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: c7ccb4a0e55440b5a83a8edd14838a20
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.239)
文档ID: c7ccb4a0e55440b5a83a8edd14838a20_keywords
检索方法: KNN
内容: 商品包装识别, 网购安全, 任务驱动, 生活化教学, 多模态技术, 角色扮演, 模拟实验, 消费维权

**检索结果 2** (融合分数: 0.199)
文档ID: c7ccb4a0e55440b5a83a8edd14838a20_teaching_modes
检索方法: KNN
内容: 教学模式1: 任务驱动模式, 0.9, 教学摘要中明确提到采用任务驱动教学法，通过"第一关"任务、小组合作、模拟网购实验等具体任务引导学生主动学习和实践，强调学生通过完成任务来掌握知识和技能。

教学模式2: 生活化教学模式, 0.8, 教学内容紧密联系生活实际，如通过"大爷买错商品"新闻片段、真伪口罩包装对比、消费者权益保护法条款等生活化案例和情境，帮助学生理解和应用所学知识。

教学模式3: ...

**检索结果 3** (融合分数: 0.192)
文档ID: c7ccb4a0e55440b5a83a8edd14838a20_subject_features
检索方法: KNN
内容: 学科：信息技术与安全教育

特征1：实践导向的教学设计 | 教学价值：通过任务驱动模式（如填写商品包装信息表、模拟网购实验）将理论知识应用于实际情境，强化学生解决真实问题的能力，符合职业教育与综合素养教育的需求。

特征2：多模态技术融合 | 教学价值：整合PPT、视频、智慧平板等技术手段，提升信息呈现的多样性与交互性，增强学生的感官体验与认知深度，适应数字化时代的教学要求。

特征3：跨学科内容...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}