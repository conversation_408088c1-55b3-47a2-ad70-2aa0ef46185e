分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: ebfac01cc8404803a8fa047319203fc2
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.207)
文档ID: ebfac01cc8404803a8fa047319203fc2_keywords
检索方法: KNN
内容: 作文点评, 线上线下, 问题导引, 小组研讨, 多元评价, 写作素养, 奇幻设定, 分层指导

**检索结果 2** (融合分数: 0.193)
文档ID: ebfac01cc8404803a8fa047319203fc2_teaching_modes
检索方法: KNN
内容: 教学模式1: 混合式学习, 0.9, 识别依据：摘要明确提到线上线下融合模式，使用钉钉直播、腾讯会议等工具进行远程互动，并结合线下白板和PPT展示，符合混合式学习的特征。
教学模式2: 合作学习, 0.8, 识别依据：摘要中提到"问题导引+小组研讨"模式，学生通过公屏实时点评、微信投票等方式进行互动，以及邀请嘉宾和学生补充点评，体现了合作学习的特点。
教学模式3: 以学生为中心, 0.85, 识别...

**检索结果 3** (融合分数: 0.191)
文档ID: ebfac01cc8404803a8fa047319203fc2_subject_features
检索方法: KNN
内容: 学科：语文（写作）

特征1：多媒体融合教学 | 教学价值：通过钉钉直播、腾讯会议、白板PPT、朗读视频等多媒体手段，实现线上线下混合式教学，增强师生互动和资源共享，提高教学效率和参与度。

特征2：问题导引与小组研讨 | 教学价值：以"动物失去特征后会发生什么"等问题引导学生思考，通过小组研讨激发学生的想象力和创造力，培养合作学习能力和批判性思维。

特征3：多元评价体系 | 教学价值：结合学生...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}