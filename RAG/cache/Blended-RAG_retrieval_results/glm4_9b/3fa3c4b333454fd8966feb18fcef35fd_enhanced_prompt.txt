分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 3fa3c4b333454fd8966feb18fcef35fd
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.255)
文档ID: 3fa3c4b333454fd8966feb18fcef35fd_keywords
检索方法: KNN
内容: 规律认知, 游戏化教学, 多媒体资源, 交互式操作, 小组讨论, 规律特征, 周期性概念, 实践创作

**检索结果 2** (融合分数: 0.211)
文档ID: 3fa3c4b333454fd8966feb18fcef35fd_subject_features
检索方法: KNN
内容: 学科：数学（或小学数学）

特征1：规律探究与模式识别 | 通过游戏化情境（如数字记忆竞赛、拖动克隆图片）和多媒体资源（PPT动态演示、视频片段），引导学生观察、归纳和表达重复规律，培养数学思维和模式识别能力。这种教学方式能激发学生兴趣，将抽象概念具象化，促进主动学习。

特征2：多模态技术支持 | 运用PPT、视频、交互式操作等技术手段，将抽象的数学规律（如周期性、有序性）转化为可视化内容，帮助...

**检索结果 3** (融合分数: 0.189)
文档ID: 3fa3c4b333454fd8966feb18fcef35fd_teaching_modes
检索方法: KNN
内容: 教学模式1: 做中学 (1.0), 识别依据：摘要中多次提到通过游戏化情境（如"脑游戏程序"、"猜规律"、"分类达人"）、交互式操作（拖动克隆图片）和实践创作（用方方卡创作规律图案）来引导学生主动探究和体验规律，符合"做中学"的核心理念。

教学模式2: 探究式学习 (0.9), 识别依据：教师在课堂上通过开放式提问、多媒体展示生活场景、小组讨论等方式激发学生的自主探究，引导学生观察、归纳和发现规...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}