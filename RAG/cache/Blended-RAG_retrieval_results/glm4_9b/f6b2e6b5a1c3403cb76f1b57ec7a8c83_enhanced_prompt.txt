分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: f6b2e6b5a1c3403cb76f1b57ec7a8c83
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.206)
文档ID: f6b2e6b5a1c3403cb76f1b57ec7a8c83_subject_features
检索方法: KNN
内容: 学科：人工智能与跨学科融合（侧重技术与人文社科结合）

特征1：技术实践导向 | 教学价值：通过Scratch编程、Arduino硬件开发等实操环节，强化学生对人脸识别、语音交互等技术原理的理解，培养动手能力和创新思维。  
特征2：问题驱动探究 | 教学价值：以"大白情感交互""动态摄像头应用"等启发性问题激发学生思考，促进自主学习和深度认知，推动技术原理与实际应用的关联。  

特征3：跨学科...

**检索结果 2** (融合分数: 0.200)
文档ID: f6b2e6b5a1c3403cb76f1b57ec7a8c83_teaching_modes
检索方法: KNN
内容: 教学模式1: 0.85, 该摘要体现了"探究式学习"模式，通过"技术认知—实践探索—创意延伸"三阶段推进，结合实物教学、多维度探究工具和启发式提问，引导学生自主发现和理解AI情感交互机制、动态摄像头等技术原理。
教学模式2: 0.80, 该摘要体现了"项目式学习"模式，通过Scratch编程调试人脸识别模块、智能手环原型设计等实践活动，让学生在解决实际问题的过程中构建知识框架，并形成兼具技术创新与...

**检索结果 3** (融合分数: 0.163)
文档ID: f6b2e6b5a1c3403cb76f1b57ec7a8c83_keywords
检索方法: KNN
内容: 人工智能, 人文智能, 问题驱动, 实践探索, 创意延伸, 人脸识别, 技术原理, AI伦理

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}