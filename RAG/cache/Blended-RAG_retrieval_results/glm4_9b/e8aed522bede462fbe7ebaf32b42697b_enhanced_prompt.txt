分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: e8aed522bede462fbe7ebaf32b42697b
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.271)
文档ID: e8aed522bede462fbe7ebaf32b42697b_keywords
检索方法: KNN
内容: 任务驱动, 分层指导, 动画制作, 技术操作, 课堂互动, 情感教育, 多媒体资源, 路径设置

**检索结果 2** (融合分数: 0.205)
文档ID: e8aed522bede462fbe7ebaf32b42697b_teaching_modes
检索方法: KNN
内容: 教学模式1: 任务驱动法, 0.9, 教学摘要中明确提到"任务驱动"和"任务驱动法"，学生通过完成动画制作任务自主学习，教师通过提问和纠正引导学生完成任务。
教学模式2: 合作学习, 0.8, 学生分组完成动画制作任务，通过抢答和讨论等形式进行合作，教师通过分组竞赛维持秩序，强调协作学习的重要性。
教学模式3: 技术整合教学, 0.85, 教学中使用了PPT、电子白板、手机拍照上传、即时反馈器等多...

**检索结果 3** (融合分数: 0.187)
文档ID: e8aed522bede462fbe7ebaf32b42697b_subject_features
检索方法: KNN
内容: 学科：信息技术（或数字媒体技术）

特征1：技术操作与创意表达结合 | 教学价值：通过PPT动画演示、电子白板等工具，学生不仅学习技术操作（如路径线设置、速度调节），还发挥创意进行动画创作，培养综合能力。

特征2：任务驱动与分层指导 | 教学价值：通过动画制作任务和分层指导，激发学生学习兴趣，促进自主探索，同时教师及时纠正错误，确保学习效果。

特征3：情境创设与跨学科融合 | 教学价值：将历史...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}