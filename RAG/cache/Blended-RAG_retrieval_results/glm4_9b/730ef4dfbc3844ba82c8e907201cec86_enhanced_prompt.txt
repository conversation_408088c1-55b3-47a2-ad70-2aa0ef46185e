分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 730ef4dfbc3844ba82c8e907201cec86
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.220)
文档ID: 730ef4dfbc3844ba82c8e907201cec86_teaching_modes
检索方法: KNN
内容: 教学模式1: 0.85, 该摘要体现了"探究式学习"模式，通过教师引导和学生主动参与（如分析实例、推导公式、提出疑问），强调自主发现和解决问题。
教学模式2: 0.75, 该摘要体现了"合作学习"模式，通过知识竞赛（抢答、必答）和师生互动，促进学生之间的协作和竞争，增强参与度。
教学模式3: 0.65, 该摘要体现了"游戏化学习"模式，通过类比"星星数量"的游戏化方式强化记忆，提高学生的学习兴趣和...

**检索结果 2** (融合分数: 0.207)
文档ID: 730ef4dfbc3844ba82c8e907201cec86_subject_features
检索方法: KNN
内容: 学科：物理学

特征1：动态演示与可视化教学 | 通过PPT动态演示转盘模型、摩天轮实例等，将抽象的圆周运动原理直观化，帮助学生建立空间想象能力，增强对向心力、线速度等概念的具象化理解。

特征2：竞赛与游戏化教学 | 采用知识竞赛、积分制抢答、游戏化问答等形式，激发学生参与热情，通过互动竞争促进知识内化，提高课堂活跃度和学习效率。

特征3：问题导向与深度探究 | 通过开放性提问（如“向心力是合...

**检索结果 3** (融合分数: 0.204)
文档ID: 730ef4dfbc3844ba82c8e907201cec86_keywords
检索方法: KNN
内容: 圆周运动, 知识竞赛, 向心力, 典型模型, 角速度, 互动教学, 公式推导, 能量守恒

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}