分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 6d628e50c5a64d0b9a57a2f3743f0427
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.232)
文档ID: 6d628e50c5a64d0b9a57a2f3743f0427_keywords
检索方法: KNN
内容: SLAM技术, 路径规划, 问题驱动, 分组实操, 教学演示, 传感器应用, 技术对比, 生活案例

**检索结果 2** (融合分数: 0.177)
文档ID: 6d628e50c5a64d0b9a57a2f3743f0427_teaching_modes
检索方法: KNN
内容: 教学模式1: 问题驱动模式, 0.9, 教学摘要中多次提到通过设问（如"GPS存在定位偏差？SLAM能解决吗？"）激发学生探究兴趣，引导学生思考，体现了以问题为中心的教学策略。
教学模式2: 任务驱动模式, 0.85, 摘要中描述了分组实操任务（如"迷宫寻路"、"地图命名、障碍物识别"），学生通过完成任务进行学习，符合任务驱动模式的特点。
教学模式3: 案例分析模式, 0.8, 教学中穿插了扫地机...

**检索结果 3** (融合分数: 0.175)
文档ID: 6d628e50c5a64d0b9a57a2f3743f0427_subject_features
检索方法: KNN
内容: 学科：计算机科学与技术（人工智能方向）  
特征1：描述 | 教学价值  
- 描述：本节课以SLAM（即时定位与地图构建）技术和路径规划为核心，通过理论讲解、案例分析、实操任务等多种形式，引导学生系统学习人工智能在导航领域的应用。教师通过播放视频、PPT讲解、设问启发等方式，结合实际案例（如高德地图、北斗系统、扫地机器人等），使学生理解SLAM技术的定义、组成、分类及其在导航中的核心作用。  
...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}