分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 77ff86010a5f444cba9c3822a601040c
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.265)
文档ID: 77ff86010a5f444cba9c3822a601040c_keywords
检索方法: KNN
内容: 拼音学习, 游戏化教学, 多媒体技术, 声母韵母, 整体认读音节, 角色扮演, 交互式白板, 即时反馈

**检索结果 2** (融合分数: 0.178)
文档ID: 77ff86010a5f444cba9c3822a601040c_subject_features
检索方法: KNN
内容: 学科：汉语（拼音教学）

特征1：游戏化教学 | 通过"拼音宝宝找教室"、"听音排队"等活动，将抽象的拼音学习转化为趣味游戏，增强学生的参与度和记忆效果。教学价值：提高学习兴趣，降低认知负荷，促进主动学习。

特征2：多媒体技术应用 | 运用PPT、动画、交互式白板等技术手段，动态演示拼音分类、发音规则等，使抽象知识具象化。教学价值：增强直观性，突破教学难点，提升课堂互动性和效率。

特征3：多模...

**检索结果 3** (融合分数: 0.169)
文档ID: 77ff86010a5f444cba9c3822a601040c_teaching_modes
检索方法: KNN
内容: 教学模式1: 游戏化教学, 置信度: 0.9, 识别依据: 摘要中多次提到通过游戏（如"拼音宝宝找教室"、"听音排队"、"标调歌谣"、"趣味竞赛"、"开火车"、"拼读接力赛"、"班长考试"）贯穿教学过程，强调学生的参与和趣味性。
教学模式2: 多媒体技术辅助教学, 置信度: 0.8, 识别依据: 摘要明确提到结合多媒体技术（如PPT动态演示、动画场景、交互式白板）实现知识系统复习和抽象知识的具象化...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}