分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 801f9539caa347c0abeb79b0b76c75cc
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.220)
文档ID: 801f9539caa347c0abeb79b0b76c75cc_keywords
检索方法: KNN
内容: 英语教学, 故事讲解, 任务驱动, CVC拼读, 情感词汇, 多媒体技术, 角色扮演, 互动白板

**检索结果 2** (融合分数: 0.196)
文档ID: 801f9539caa347c0abeb79b0b76c75cc_teaching_modes
检索方法: KNN
内容: 教学模式1: 0.8, 识别依据：该摘要体现了"建构主义学习理论"，通过多媒体技术、游戏化教学和情境化问题引导学生主动探索和构建知识，如大转盘游戏强化单词记忆、思维导图梳理故事要素等。
教学模式2: 0.7, 识别依据：该摘要体现了"任务驱动教学法"，通过分组协作任务（如"put them on yes or no"）和创作任务（如"写书并分享"）驱动学生学习，强调实践和产出。
教学模式3: 0....

**检索结果 3** (融合分数: 0.194)
文档ID: 801f9539caa347c0abeb79b0b76c75cc_subject_features
检索方法: KNN
内容: 学科：英语

特征1：多媒体与游戏化教学融合 | 描述：本节课通过PPT、音频、互动白板等多媒体技术结合大转盘游戏等游戏化手段，增强教学的趣味性和互动性。例如，利用PPT展示绘本封面和情节，通过游戏强化单词记忆。教学价值：提高学生的学习兴趣和参与度，使抽象的语言规则（如CVC拼读）更直观易懂，促进知识的深度记忆。

特征2：任务驱动与情境化问题设计 | 描述：教师设计了多种任务，如故事排序、判断练...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}