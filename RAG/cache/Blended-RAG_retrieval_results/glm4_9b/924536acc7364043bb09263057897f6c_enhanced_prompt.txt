分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 924536acc7364043bb09263057897f6c
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.216)
文档ID: 924536acc7364043bb09263057897f6c_keywords
检索方法: KNN
内容: 立体交通, 科幻主题, 线上线下融合, 数字绘画, 手绘示范, 分层指导, 即时反馈, 创作意识

**检索结果 2** (融合分数: 0.206)
文档ID: 924536acc7364043bb09263057897f6c_subject_features
检索方法: KNN
内容: 学科：美术（数字绘画与空间表现）

特征1：多媒体融合教学 | 教学价值：通过线上线下融合模式，结合直播平台（丁丁在线）、数字绘画软件（Procreate）及传统手绘示范，突破时空限制，增强教学的灵活性与互动性，同时满足不同学生的学习需求。

特征2：分层指导与即时反馈 | 教学价值：教师通过连麦、屏幕共享等技术手段实时观察学生作品，针对不同进度和问题的学生进行个性化指导（如构图调整、细节补充），...

**检索结果 3** (融合分数: 0.180)
文档ID: 924536acc7364043bb09263057897f6c_teaching_modes
检索方法: KNN
内容: 教学模式1: 建构主义, 0.85, 识别依据：教师通过观察学生作品并提供实时反馈，引导学生自主发现问题并调整创作，强调学生在学习过程中的主动建构。同时，结合示范和提问，鼓励学生阐述创作思路，体现了知识内化和意义建构的过程。

教学模式2: 混合式学习, 0.80, 识别依据：课程采用线上线下融合模式，结合丁丁在线平台直播教学和Procreate数字绘画工具，以及手绘示范，整合了线上资源和线下互动...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}