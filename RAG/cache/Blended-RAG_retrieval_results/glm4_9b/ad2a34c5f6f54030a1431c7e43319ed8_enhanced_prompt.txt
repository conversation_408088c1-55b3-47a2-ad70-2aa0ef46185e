分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: ad2a34c5f6f54030a1431c7e43319ed8
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.214)
文档ID: ad2a34c5f6f54030a1431c7e43319ed8_teaching_modes
检索方法: KNN
内容: 教学模式1: 任务驱动模式, 0.85, 教学摘要中明确提到采用"任务驱动"模式，通过"屋顶类型识别"等问题引导学生观察和分析，体现了以任务为导向的教学策略。
教学模式2: 多媒体整合模式, 0.80, 教学过程中大量使用PPT、视频、模型等多媒体资源进行教学，结合图像连线、视频展示和PPT动态演示等方式，符合多媒体整合模式的特点。
教学模式3: 探究式学习模式, 0.75, 学生通过分析太和殿梁...

**检索结果 2** (融合分数: 0.214)
文档ID: ad2a34c5f6f54030a1431c7e43319ed8_keywords
检索方法: KNN
内容: 传统建筑, 任务驱动, 3D模型, 视频解析, 小组讨论, 结构认知, 等级制度, 美学特征

**检索结果 3** (融合分数: 0.204)
文档ID: ad2a34c5f6f54030a1431c7e43319ed8_subject_features
检索方法: KNN
内容: 学科：建筑学（传统文化与历史）  

特征1：结构化知识体系 | 教学价值：通过故宫建筑群切入，结合《中国建筑史》教材、3D打印模型等资源，系统解析建筑特征（如屋顶类型、梁柱布局），帮助学生建立完整的知识框架，培养系统性思维。  
特征2：多媒体技术融合 | 教学价值：运用图像连线、视频展示、PPT动态演示、微课视频等技术手段，直观呈现复杂建筑结构（如脊线形成原理），提升学习兴趣与理解效率。  
...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}