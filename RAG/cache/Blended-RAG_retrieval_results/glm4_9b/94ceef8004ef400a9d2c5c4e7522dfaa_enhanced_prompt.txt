分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 94ceef8004ef400a9d2c5c4e7522dfaa
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.199)
文档ID: 94ceef8004ef400a9d2c5c4e7522dfaa_teaching_modes
检索方法: KNN
内容: 教学模式1: 混合式学习, 0.9, 教学中结合了传统教学手段（如PPT展示）和现代技术手段（如智慧教育平台、平板电脑），体现了线上线下融合的教学特点。
教学模式2: 合作学习, 0.8, 教学过程中多次提到小组合作探究（如在线批注、投屏共享、小组PK），强调学生之间的互动与合作。
教学模式3: 深度学习, 0.85, 教师通过提问式教学、情境创设、文本细节分析等活动，引导学生深入理解文本，达成深...

**检索结果 2** (融合分数: 0.176)
文档ID: 94ceef8004ef400a9d2c5c4e7522dfaa_keywords
检索方法: KNN
内容: 智慧教育, 多媒体资源, 圈画批注, 情感体验, 小组合作, 文本解读, 深度学习, 互动游戏

**检索结果 3** (融合分数: 0.169)
文档ID: 94ceef8004ef400a9d2c5c4e7522dfaa_subject_features
检索方法: KNN
内容: 学科：语文  
特征1：多媒体资源整合与智慧教育平台应用 | 教学价值：通过智慧教育平台整合多媒体资源，如荷兰风光视频、牧场场景图等，激发学生兴趣，增强课堂的直观性和生动性，使抽象的语言文字变得形象化，有助于学生更好地理解和感受文本内容。  
特征2：圈画批注与情感体验相结合 | 教学价值：通过圈画批注的方式引导学生深入文本，结合情感体验，帮助学生把握文本的核心意象和情感基调，如“牛群吃草、骏马飞...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}