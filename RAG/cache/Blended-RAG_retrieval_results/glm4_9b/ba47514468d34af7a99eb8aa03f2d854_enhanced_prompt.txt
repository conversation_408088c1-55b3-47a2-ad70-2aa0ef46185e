分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: ba47514468d34af7a99eb8aa03f2d854
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.253)
文档ID: ba47514468d34af7a99eb8aa03f2d854_keywords
检索方法: KNN
内容: 昆虫分类, 互动探究, 技术融合, 情境创设, 角色扮演, 多元表征, 生态功能, 教学模式

**检索结果 2** (融合分数: 0.207)
文档ID: ba47514468d34af7a99eb8aa03f2d854_subject_features
检索方法: KNN
内容: 学科：生物学（语文教学融合）

特征1：情境创设与沉浸式教学 | 通过森林背景图、鸟鸣音效、放大镜道具等构建真实情境，激发学生兴趣，增强学习体验，促进主动参与。
特征2：技术融合与互动探究 | 利用星沃白板、班级优化大师、活照线APP等技术工具，实现生字词检测、随机抽选、个性化表达等功能，提升课堂互动性和效率。
特征3：分类与比较教学 | 引导学生识别蜻蜓、瓢虫、独角仙、蚂蚱四种昆虫，结合多音字"...

**检索结果 3** (融合分数: 0.183)
文档ID: ba47514468d34af7a99eb8aa03f2d854_teaching_modes
检索方法: KNN
内容: 教学模式1: 0.85, 该摘要体现了"探究式学习"模式，通过情境创设、技术融合与互动探究展开教学，强调学生的主动参与和知识建构。例如，使用放大镜道具、星沃白板功能、活照线APP等工具，以及"东东求助"情境，都旨在激发学生的探究欲望和自主学习能力。
教学模式2: 0.75, 该摘要体现了"混合式学习"模式，通过线上技术与线下教学的融合，如希沃白板、星沃白板、活照线APP等技术工具的应用，结合传统的...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}