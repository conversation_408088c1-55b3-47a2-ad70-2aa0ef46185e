分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: c47bdfe1bb0c4a9181796697adb32f52
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.234)
文档ID: c47bdfe1bb0c4a9181796697adb32f52_keywords
检索方法: KNN
内容: 字母发音, 小组竞赛, 角色扮演, 任务驱动, 书写训练, 互动式教学, 即时反馈, 情境模拟

**检索结果 2** (融合分数: 0.195)
文档ID: c47bdfe1bb0c4a9181796697adb32f52_teaching_modes
检索方法: KNN
内容: 教学模式1: 互动式教学, 0.9, 识别依据：摘要中强调师生互动、小组竞赛、角色扮演、即时反馈等元素，突出学生与教师之间的动态交流和协作，符合互动式教学的核心特征。
教学模式2: 任务驱动教学, 0.8, 识别依据：摘要提到购物游戏、积分制等任务设计，旨在通过具体任务引导学生主动学习和实践，体现任务驱动教学的特征。
教学模式3: 情境模拟教学, 0.7, 识别依据：摘要描述了利用西卧平台模拟生活...

**检索结果 3** (融合分数: 0.186)
文档ID: c47bdfe1bb0c4a9181796697adb32f52_subject_features
检索方法: KNN
内容: 学科：英语（小学阶段）

特征1：描述 | 教学价值  
描述：本节课以字母发音与书写为核心，通过游戏化教学（如字母树寻找、购物游戏）和竞赛机制（积分制、小组竞赛）激发学生学习兴趣，同时结合《My ABCs》歌曲创设情境，强化语言学习的趣味性。  
教学价值：提高学生参与度和主动性，降低学习焦虑，符合儿童认知特点，促进语言技能的全面发展。

特征2：描述 | 教学价值  
描述：教师运用分层教学技...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}