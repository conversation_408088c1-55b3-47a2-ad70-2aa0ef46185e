分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: fc7305c146ac488e96e807f95f6f710c
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.186)
文档ID: fc7305c146ac488e96e807f95f6f710c_teaching_modes
检索方法: KNN
内容: 教学模式1: 探究式学习, 0.8, 识别依据：教师在课堂上通过提问"为什么不同树叶会有不同声音"，引导学生通过实例建立认知链，并布置课后观察任务，鼓励学生自主探究自然声音。这种教学方式强调学生的主动发现和知识建构，符合探究式学习的特征。
教学模式2: 任务驱动学习, 0.7, 识别依据：教师设计了"厨房音乐厅"创编和"自然歌手"诗歌创作等具体任务，学生通过完成任务进行学习和表达，体现了任务驱动学...

**检索结果 2** (融合分数: 0.174)
文档ID: fc7305c146ac488e96e807f95f6f710c_keywords
检索方法: KNN
内容: 情境导入,文本研读,任务驱动,创意延伸,拟声词,Pad平台,声音层次感,感官差异

**检索结果 3** (融合分数: 0.169)
文档ID: fc7305c146ac488e96e807f95f6f710c_subject_features
检索方法: KNN
内容: 学科：语文（小学阶段）

特征1：情境导入与感官体验 | 通过拟声词创设听觉场景，引导学生朗读课文感受自然视听交融，增强学生对自然声音的直观感知，激发学习兴趣，培养审美能力。

特征2：任务驱动与探究学习 | 运用Pad平台布置"厨房音乐厅"创编和"自然歌手"诗歌创作等任务，让学生通过拖拽操作和诗歌创作主动探究声音层次感，培养创造性思维和实践能力。

特征3：对比分析与具象认知 | 通过对比"微风...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}