分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 7df7c07c7e88450ca238e00bb0dbd87b
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.207)
文档ID: 7df7c07c7e88450ca238e00bb0dbd87b_keywords
检索方法: KNN
内容: 网络技术应用, 竞赛闯关, 自主练习, 知识梳理, 实物演示, 分组竞赛, 在线测试, 错题讲解

**检索结果 2** (融合分数: 0.201)
文档ID: 7df7c07c7e88450ca238e00bb0dbd87b_subject_features
检索方法: KNN
内容: 学科：信息技术（网络技术应用）

特征1：理论与实践相结合 | 通过实物演示（双绞线、光缆）、拓扑图解析、调制解调器工作原理解析等实践操作，帮助学生直观理解抽象的网络技术概念，增强动手能力和问题解决能力。

特征2：分层递进的教学设计 | 采用"知识梳理+竞赛闯关+自主练习"三阶段模式，结合分层指导和即时反馈（如五星练习系统、勋章奖励），满足不同学生的学习需求，提升学习效果。

特征3：情境化与生...

**检索结果 3** (融合分数: 0.193)
文档ID: 7df7c07c7e88450ca238e00bb0dbd87b_teaching_modes
检索方法: KNN
内容: 教学模式1: 0.8, 识别依据：摘要中体现了"知识梳理+竞赛闯关+自主练习"的三阶段模式，强调通过视频解析、PPT对比、实物演示等方式进行知识梳理，并通过小组竞赛和即时评分等互动环节促进学生参与，符合探究式学习和合作学习的特点。
教学模式2: 0.7, 识别依据：摘要中提到使用技术工具如PPT、Amara.org字幕平台和在线测试系统，以及动态演示HTTP/FTP端口对应关系，这些均体现了技术增...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}