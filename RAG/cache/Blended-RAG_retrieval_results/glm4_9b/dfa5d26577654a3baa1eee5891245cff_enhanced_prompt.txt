分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: dfa5d26577654a3baa1eee5891245cff
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.220)
文档ID: dfa5d26577654a3baa1eee5891245cff_subject_features
检索方法: KNN
内容: 学科：物理

特征1：实验探究与理论讲解相结合 | 通过实验（如两本书叠加、脚踩地面摩擦力方向）和生活实例（推墙、传送带）引导学生理解摩擦力的本质和特性，强化了实践与理论的统一，帮助学生建立直观认识并深化理论理解。

特征2：动态教学与实时反馈 | 利用朗维系统和平板实时答题系统收集学生反馈，教师能动态调整教学策略，同时通过测力计、传感器及“抹茶粒”软件分屏显示数据变化，增强了教学的互动性和即时性...

**检索结果 2** (融合分数: 0.183)
文档ID: dfa5d26577654a3baa1eee5891245cff_teaching_modes
检索方法: KNN
内容: 教学模式1: 探究式学习, 置信度0.9, 识别依据：摘要中多次提到实验探究（如"两本书叠加能否被拉开"实验、"传感器实验装置直观演示静摩擦力变化过程"），强调学生通过动手操作和观察现象自主发现知识，符合探究式学习的特征。
教学模式2: 混合式学习, 置信度0.85, 识别依据：摘要中结合了实验探究（如朗维系统动态演示、传感器实验）和理论讲解（如PPT动画展示摩擦力方向判断模型），同时运用技术工具...

**检索结果 3** (融合分数: 0.150)
文档ID: dfa5d26577654a3baa1eee5891245cff_keywords
检索方法: KNN
内容: 摩擦力, 实验探究, 理论讲解, 滑动摩擦力, 静摩擦力, 传感器实验, 师生互动, 技术工具

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}