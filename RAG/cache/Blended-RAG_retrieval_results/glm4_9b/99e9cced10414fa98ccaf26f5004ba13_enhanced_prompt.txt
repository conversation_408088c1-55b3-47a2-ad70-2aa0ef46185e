分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 99e9cced10414fa98ccaf26f5004ba13
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.212)
文档ID: 99e9cced10414fa98ccaf26f5004ba13_subject_features
检索方法: KNN
内容: 学科：语文（语言文字运用）  

特征1：语境化教学 | 通过创设具体语境（如"日理万机"与学习任务的对比、"牙"字形近字解析）帮助学生理解词语的准确含义和使用范围，避免脱离实际的抽象讲解，提升语言运用的精准性。  
特征2：多模态资源整合 | 运用PPT动态示意图、视频素材、实验室模拟装置等多种多媒体资源，结合具象化比喻（如"狗尾草""吹毛求疵"），增强感官体验，促进知识的形象化理解和记忆。  ...

**检索结果 2** (融合分数: 0.208)
文档ID: 99e9cced10414fa98ccaf26f5004ba13_keywords
检索方法: KNN
内容: 成语误用, 语境辨析, 情境导入, 案例分析, 互动探究, 多媒体资源, 分组讨论, 文化理解

**检索结果 3** (融合分数: 0.189)
文档ID: 99e9cced10414fa98ccaf26f5004ba13_teaching_modes
检索方法: KNN
内容: 教学模式1: 0.85, 案例分析-情境导入-互动探究模式。摘要中明确提到采用"情境导入-案例分析-互动探究"模式，并通过具体案例（如"日理万机"、"牙"字形近字）和语境对比进行教学，符合该模式的特征。
教学模式2: 0.75, 分层教学-多媒体辅助教学。摘要中提到教师通过分层提问、数据反馈和多媒体资源（实验室模拟装置、视频片段）进行教学，并结合分组讨论和即时反馈调节节奏，体现了分层教学和多媒体辅...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}