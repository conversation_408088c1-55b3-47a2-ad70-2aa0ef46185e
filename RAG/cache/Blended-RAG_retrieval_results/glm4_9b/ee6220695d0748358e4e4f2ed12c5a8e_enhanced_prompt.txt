分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: ee6220695d0748358e4e4f2ed12c5a8e
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.219)
文档ID: ee6220695d0748358e4e4f2ed12c5a8e_keywords
检索方法: KNN
内容: 文本细读, 多元教学, 文化对比, 任务驱动, 互动节奏, 仿写练习, 技术融合, 生活场景

**检索结果 2** (融合分数: 0.182)
文档ID: ee6220695d0748358e4e4f2ed12c5a8e_teaching_modes
检索方法: KNN
内容: 教学模式1: 0.85, 识别依据：摘要中强调多维度教学设计、技术手段（智慧计时器、Amara.org实时字幕、摇光器）与教学目标的深度融合，以及通过任务驱动法（"详略对比"）、小组竞赛、启发式提问等形式促进学生主动学习和知识建构，符合建构主义教学模式的特点。
教学模式2: 0.75, 识别依据：摘要中提到课前预习检测、课堂小组讨论、文本细读与思维训练，以及教师适时纠正和解析文化内涵，体现了探究式...

**检索结果 3** (融合分数: 0.175)
文档ID: ee6220695d0748358e4e4f2ed12c5a8e_subject_features
检索方法: KNN
内容: 学科：语文（中国文学与传统文化）

特征1：文本细读与主题探究 | 教学价值：通过分析《北京的春节》中腊八、小年等节点的详写内容，结合第八自然段的细节，学生深入理解"热闹""喜庆""团圆"的主题意象，培养对文学作品中情感与文化的敏感度。这种教学方式有助于提升学生的文学鉴赏能力和批判性思维。

特征2：文化对比与跨文化理解 | 教学价值：通过王安石《元日》引入春节习俗差异，引导学生关注不同地域和民族...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}