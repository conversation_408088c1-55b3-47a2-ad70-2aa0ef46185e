分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: c9d0f0e6f1314456b4e444b4875d53fc
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.260)
文档ID: c9d0f0e6f1314456b4e444b4875d53fc_keywords
检索方法: KNN
内容: 有序搭配,排列组合,问题链,可视化教学,乘法原理,符号系统,动态演示,认知跃迁

**检索结果 2** (融合分数: 0.195)
文档ID: c9d0f0e6f1314456b4e444b4875d53fc_subject_features
检索方法: KNN
内容: 学科：数学（组合数学/离散数学）

特征1：情境化教学设计 | 通过"小红穿衣搭配"和"早餐搭配"等生活化情境引入排列组合概念，使抽象数学原理变得具象可感，增强学习兴趣和实际应用意识。

特征2：可视化与符号化教学 | 运用图形表示法、连线法及三角形区分色系等符号系统，将复杂组合关系转化为直观视觉形式，降低认知负荷，促进思维外化。

特征3：问题链梯度推进 | 以"快餐店套餐组合→密码锁安全性→路...

**检索结果 3** (融合分数: 0.183)
文档ID: c9d0f0e6f1314456b4e444b4875d53fc_teaching_modes
检索方法: KNN
内容: 教学模式1: 递进式探究学习, 置信度: 0.9, 识别依据: 摘要中明确提到"三个层次递进式教学设计"和"问题链"策略，通过情境引导和学生自主探索逐步深化对排列组合原理的理解，符合递进式探究学习的特征。
教学模式2: 技术增强型合作学习, 置信度: 0.8, 识别依据: 教学中大量使用智慧云平台、平板设备和PPT等技术工具，并结合"人机对战"和"同伴互评"环节，强调技术支持下的学生互动与合作。
...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}