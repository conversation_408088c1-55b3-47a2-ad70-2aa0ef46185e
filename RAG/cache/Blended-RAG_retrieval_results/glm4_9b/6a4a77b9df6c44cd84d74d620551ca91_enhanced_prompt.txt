分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 6a4a77b9df6c44cd84d74d620551ca91
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.253)
文档ID: 6a4a77b9df6c44cd84d74d620551ca91_keywords
检索方法: KNN
内容: 高中英语, 形容词副词, 高考真题, 即时反馈, 教学模式, 例题分析, 课堂互动, 技术应用

**检索结果 2** (融合分数: 0.205)
文档ID: 6a4a77b9df6c44cd84d74d620551ca91_teaching_modes
检索方法: KNN
内容: 教学模式1: 基于问题的学习 (Problem-Based Learning), 置信度: 0.8, 识别依据: 教师通过高考真题解析导入，结合实际案例（如"stronger"与"more powerful"的语义演变）引导学生探究问题，并通过对比分析（如"close/closely"发音差异）促进学生自主发现和解决问题。
教学模式2: 交互式学习 (Interactive Learning), ...

**检索结果 3** (融合分数: 0.181)
文档ID: 6a4a77b9df6c44cd84d74d620551ca91_subject_features
检索方法: KNN
内容: 学科：英语

特征1：语法系统化教学 | 教学价值：通过解析比较级/最高级的考查规律、归纳副词三大用法体系、区分抽象概念（如far/further），帮助学生建立系统的语法知识框架，提升语言运用的准确性和逻辑性。

特征2：高考导向性教学 | 教学价值：以高考真题解析导入，结合真题示范和易错点解析，使学生熟悉考试形式和难度，增强应试能力，同时通过对比分析（如stronger与more powerf...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}