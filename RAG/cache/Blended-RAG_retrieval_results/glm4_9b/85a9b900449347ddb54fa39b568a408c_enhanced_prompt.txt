分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 85a9b900449347ddb54fa39b568a408c
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.215)
文档ID: 85a9b900449347ddb54fa39b568a408c_keywords
检索方法: KNN
内容: 几何最短路径, 轨迹分析, 动态模型, 几何画板, 问题驱动, 技术辅助, 定位分析, 空间想象

**检索结果 2** (融合分数: 0.204)
文档ID: 85a9b900449347ddb54fa39b568a408c_subject_features
检索方法: KNN
内容: 学科：数学（几何）

特征1：动态模型构建与可视化教学 | 通过几何画板等工具动态演示几何图形的运动与变化，帮助学生直观理解抽象的几何概念（如轨迹、最短路径），增强空间想象能力。这种教学方式使复杂问题变得简单化，促进学生对几何性质的理解和应用。

特征2：问题驱动式教学 | 以实际问题（如“坠坠问题”）引入，通过问题链（如“谁是定点”“谁是动点”）引导学生逐步深入探究，激发学生的学习兴趣和主动性。...

**检索结果 3** (融合分数: 0.204)
文档ID: 85a9b900449347ddb54fa39b568a408c_teaching_modes
检索方法: KNN
内容: 教学模式1: 问题驱动模式, 置信度0.9, 识别依据：摘要中多次提到"问题驱动"，如"坠坠问题"引入、学生对相似三角形的困惑以及开放式提问"你发现了吗""谁来举例"，这些均体现了以问题为中心的教学策略。
教学模式2: 技术辅助模式, 置信度0.8, 识别依据：摘要强调大量运用几何画板进行动态演示，如"动态模型构建"、"几何画板实时演示"等，表明技术手段在教学中起关键作用。
教学模式3: 探究式学...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}