分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: cd271c45ac6546288874bb27f1c43256
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.262)
文档ID: cd271c45ac6546288874bb27f1c43256_keywords
检索方法: KNN
内容: 任务驱动, AR技术, 生字教学, 角色扮演, 合作学习, 情境模拟, 个性化学习, 拆解结构

**检索结果 2** (融合分数: 0.194)
文档ID: cd271c45ac6546288874bb27f1c43256_subject_features
检索方法: KNN
内容: 学科：语文

特征1：多媒体技术与学科内容深度融合 | 教学价值：通过AR技术、微课视频、动画视频等创设虚拟场景和情境，增强学习的趣味性和直观性，激发学生学习兴趣，同时使抽象的文字知识变得生动易懂，提高学习效率。

特征2：任务驱动与情境模拟相结合 | 教学价值：设计“解锁绳子”“解锁路线”“解锁终点”等关卡任务，结合动画配音、角色扮演等情境模拟活动，让学生在完成任务的过程中主动探究和学习，培养学...

**检索结果 3** (融合分数: 0.168)
文档ID: cd271c45ac6546288874bb27f1c43256_teaching_modes
检索方法: KNN
内容: 教学模式1: 任务驱动教学, 0.9, 识别依据：摘要中明确提到教学分为"解锁绳子""解锁路线""解锁终点"三关卡任务，通过任务驱动的方式引导学生完成学习目标，如多音字辨析、叙事顺序梳理、动词具象化等，体现了任务驱动的核心特征。
教学模式2: 情境模拟教学, 0.8, 识别依据：教师利用AR技术创设虚拟场景，结合动画配音、角色扮演等活动，为学生提供沉浸式的学习环境，模拟真实情境，增强学习的趣味性和...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}