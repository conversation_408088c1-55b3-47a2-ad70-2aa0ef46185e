分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 60c3c12846aa47d7a0f4b383dacbbf1b
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.253)
文档ID: 60c3c12846aa47d7a0f4b383dacbbf1b_keywords
检索方法: KNN
内容: 多媒体, 交通, 词汇教学, 情境模拟, 小组竞赛, 问题引导, 认知应用, 总结表格

**检索结果 2** (融合分数: 0.190)
文档ID: 60c3c12846aa47d7a0f4b383dacbbf1b_teaching_modes
检索方法: KNN
内容: 教学模式1: 0.85, 该摘要体现了"建构主义学习理论"，通过多媒体创设情境、分阶段推进教学，强调学生在真实情境中主动构建知识体系，如情境模拟、小组竞赛等活动均支持学生自主探究。
教学模式2: 0.75, 该摘要体现了"任务型语言教学(TBLT)"，通过具体任务（如路线分析、词汇匹配）驱动学习，教师设计多个任务链（听力训练→词汇解析→情境模拟→文件类词汇→小组竞赛），逐步提升语言应用能力。
教学...

**检索结果 3** (融合分数: 0.175)
文档ID: 60c3c12846aa47d7a0f4b383dacbbf1b_subject_features
检索方法: KNN
内容: 学科：英语（综合技能课）

特征1：多媒体情境创设 | 通过PPT、视频、音频等多媒体手段创设北京交通主题情境，激发学习兴趣，降低语言输入难度，符合语言学习中的沉浸式原则。
特征2：分层递进式教学 | 分五个阶段推进教学，从听力训练到词汇解析，再到情境模拟和法律文书认知，最后通过竞赛巩固，体现认知规律，逐步提升学生综合能力。
特征3：任务型教学活动 | 设计"match words with Ch...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}