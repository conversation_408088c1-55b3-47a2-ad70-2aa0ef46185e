分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: 945906df2cf142a3afd3472e5c06daac
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.226)
文档ID: 945906df2cf142a3afd3472e5c06daac_keywords
检索方法: KNN
内容: 线上线下融合, 鲁迅故乡, 人物形象分析, 文本细读, 小组讨论, 技术辅助教学, 乡土文化, 教学策略

**检索结果 2** (融合分数: 0.191)
文档ID: 945906df2cf142a3afd3472e5c06daac_teaching_modes
检索方法: KNN
内容: 教学模式1: 混合式学习, 0.9, 识别依据：摘要明确提到“线上线下融合模式”，课前线上任务与AI数据分析，课中电子白板等技术工具辅助，符合混合式学习的特征。
教学模式2: 探究式学习, 0.8, 识别依据：学生通过小组讨论、电子白板填写信息表等活动主动探究人物形象和象征意义，教师引导而非直接灌输知识。
教学模式3: 合作学习, 0.7, 识别依据：小组讨论环节明确提到学生合作填写人物信息表，教...

**检索结果 3** (融合分数: 0.183)
文档ID: 945906df2cf142a3afd3472e5c06daac_subject_features
检索方法: KNN
内容: 学科：语文（文学）

特征1：文本细读与分析 | 教学价值：通过解析"一吐一闻"、"彩虹"、"小树"等环境描写，以及"紫色圆脸"、"银项圈"等外貌特征，培养学生对文本细节的敏感度，提升文学鉴赏能力，理解作者如何通过细节塑造人物形象。

特征2：线上线下融合教学模式 | 教学价值：课前线上练笔任务结合AI数据分析，精准定位教学起点；课堂中运用电子白板、PPT等技术工具，实现教学资源的有效整合，提高教...

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}