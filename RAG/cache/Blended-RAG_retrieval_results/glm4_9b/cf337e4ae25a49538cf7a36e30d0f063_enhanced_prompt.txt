分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: cf337e4ae25a49538cf7a36e30d0f063
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.184)
文档ID: cf337e4ae25a49538cf7a36e30d0f063_subject_features
检索方法: KNN
内容: 学科：语文

特征1：情境创设与文本解读 | 通过创设"宇宙的另一边"情境和开放性问题激发学生兴趣，帮助学生聚焦核心意象，促进对文本的深度理解，培养阅读兴趣和批判性思维。

特征2：技术融合与具象化教学 | 利用AR技术和PPT展示等手段将抽象概念具象化，增强学生的沉浸感和学习体验，提高对复杂概念的认知效果。

特征3：游戏化与角色扮演 | 通过词语接龙、角色扮演等游戏化活动，强化基础词汇，深化多...

**检索结果 2** (融合分数: 0.183)
文档ID: cf337e4ae25a49538cf7a36e30d0f063_teaching_modes
检索方法: KNN
内容: 教学模式1: 0.85, 识别依据：该摘要体现了“探究式学习”模式，通过开放性问题、AR技术和角色扮演等方式引导学生主动探索和思考，强调学生的主体性和深度参与。
教学模式2: 0.75, 识别依据：该摘要展示了“情境教学法”，通过创设“宇宙的另一边”的情境，结合视觉素材和创意游戏，帮助学生将抽象概念具象化，增强理解和记忆。
教学模式3: 0.65, 识别依据：该摘要反映了“混合式学习”模式，结合了...

**检索结果 3** (融合分数: 0.149)
文档ID: cf337e4ae25a49538cf7a36e30d0f063_keywords
检索方法: KNN
内容: 宇宙想象, AR技术, 文本解读, 角色扮演, 开放提问, 创意游戏, 深度思考, 情境创设

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}