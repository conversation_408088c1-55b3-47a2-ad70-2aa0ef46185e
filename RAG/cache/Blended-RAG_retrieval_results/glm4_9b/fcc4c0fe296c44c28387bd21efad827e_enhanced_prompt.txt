分析课堂录音，识别主要教学环节转换点。

课程：{lesson_name}

要求：
1. 只识别教学环节转换点，不要逐句分析录音内容
2. 严格控制在3-8个主要教学环节，每个环节持续5-15分钟
3. 输出格式：HH:MM:SS 教学环节; （每个时间点单独一行，以分号结尾）
4. 时间点必须来自录音文本中的实际时间戳
5. 环节名称必须使用具体的标准教学术语
6. 严禁使用"教学环节"、"具体环节名称"等占位符，必须输出实际的环节名称
7. 严禁相邻的环节是重复相同的环节名称，相邻重复的环节名称需要合并
8. 忽略具体说话内容，只关注教学活动的重大转换
9. 禁止对每个时间段进行详细描述，只标记主要教学阶段
10. 一节课只有几个大的教学环节，不要把每句话都当作环节

输出格式示例：
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;
00:XX:XX 具体环节名称;

## Blended-RAG参考信息 
以下信息通过Blended-RAG多方法融合检索获得，用于辅助理解教学模式和时间特征：

### 录音文本ID: fcc4c0fe296c44c28387bd21efad827e
### 模型: GLM4_9B
### 检索方法: Blended-RAG (BM25 + KNN + ELSER)

以下是Blended-RAG多方法融合检索的结果：

**检索结果 1** (融合分数: 0.209)
文档ID: fcc4c0fe296c44c28387bd21efad827e_teaching_modes
检索方法: KNN
内容: 教学模式1: 0.85, 识别依据：该教学摘要体现了探究式学习模式，通过问题链引导学生逐步建构知识体系，强调学生的主动探索和发现过程。例如，通过手机屏幕拖拽功能开展除法算式分类活动，以及小组讨论解决"0的因数是否存在"问题，都显示了学生在教师引导下自主探究的过程。
教学模式2: 0.75, 识别依据：该教学摘要体现了建构主义学习模式，通过激活旧知、实例阐释和多层练习，帮助学生逐步构建对"因数与倍数...

**检索结果 2** (融合分数: 0.196)
文档ID: fcc4c0fe296c44c28387bd21efad827e_subject_features
检索方法: KNN
内容: 学科：数学  
特征1：概念建构与问题驱动 | 通过问题链引导学生逐步建构"因数与倍数"的核心概念，利用旧知激活思维，通过分类活动和追问发现非整除共性，教学价值在于培养学生自主探究和知识迁移能力。  
特征2：技术辅助可视化 | 运用PPT、手机拖拽和作业发布系统等技术工具，将抽象概念具象化，教学价值在于提升学习体验和概念理解的直观性。  
特征3：分层递进练习 | 设置从基础判断到开放性任务的逐...

**检索结果 3** (融合分数: 0.184)
文档ID: fcc4c0fe296c44c28387bd21efad827e_keywords
检索方法: KNN
内容: 因数与倍数, 问题链, 概念建构, 多层练习, 互动模式, 技术工具, 认知偏差, 有序列举

**Blended-RAG检索指导:**
1. **BM25检索**: 基于关键词匹配的传统检索方法
2. **KNN检索**: 基于语义向量的相似度检索方法
3. **ELSER检索**: 基于稀疏编码器的高级检索方法
4. **融合策略**: 多方法结果的加权融合和排序优化
5. **教学应用**: 结合关键词匹配和语义理解识别教学环节
6. **质量保证**: 多方法验证确保检索结果的准确性

**检索统计**: KNN: 3



## 录音文本
{transcript_content}