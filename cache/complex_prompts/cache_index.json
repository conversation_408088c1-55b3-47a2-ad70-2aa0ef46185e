{"created_at": "2025-07-11T07:08:07.900063", "cache_entries": {"76e10dbffd09396d": {"lesson_id": "214630d987e04a27bff4d646de7999a7", "template_type": "complex", "file_path": "cache/complex_prompts/214630d987e04a27bff4d646de7999a7_complex_prompt.txt", "cached_at": "2025-07-13T10:38:35.569971", "prompt_length": 997, "metadata": {"lesson_name": "罗斯福新政", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:38:35.569804"}}, "b9fc5092e84c2065": {"lesson_id": "99e9cced10414fa98ccaf26f5004ba13", "template_type": "complex", "file_path": "cache/complex_prompts/99e9cced10414fa98ccaf26f5004ba13_complex_prompt.txt", "cached_at": "2025-07-13T10:38:41.840119", "prompt_length": 963, "metadata": {"lesson_name": "词语的理解和使用", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:38:41.839957"}}, "caa153948ae7a9d2": {"lesson_id": "eb72c75a646540ef9a862f1bcf4d481b", "template_type": "complex", "file_path": "cache/complex_prompts/eb72c75a646540ef9a862f1bcf4d481b_complex_prompt.txt", "cached_at": "2025-07-13T10:38:48.068713", "prompt_length": 968, "metadata": {"lesson_name": "第15课 明至清中叶的经济与文化", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:38:48.068551"}}, "2ff2ff080c53af3e": {"lesson_id": "228f4a6ea4254a54b861419ca98c5b20", "template_type": "complex", "file_path": "cache/complex_prompts/228f4a6ea4254a54b861419ca98c5b20_complex_prompt.txt", "cached_at": "2025-07-13T10:38:54.491176", "prompt_length": 860, "metadata": {"lesson_name": "人体概述", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:38:54.491012"}}, "aa66aaae937b1222": {"lesson_id": "b84f5f6acd7d4576a82cb8c6ea5b9c23", "template_type": "complex", "file_path": "cache/complex_prompts/b84f5f6acd7d4576a82cb8c6ea5b9c23_complex_prompt.txt", "cached_at": "2025-07-13T10:39:00.788075", "prompt_length": 1003, "metadata": {"lesson_name": "书间精灵——藏书票", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:39:00.787916"}}, "d27f31b44f0c8658": {"lesson_id": "13b63d5b53674ff68e31d19f86b18d14", "template_type": "complex", "file_path": "cache/complex_prompts/13b63d5b53674ff68e31d19f86b18d14_complex_prompt.txt", "cached_at": "2025-07-13T10:39:06.944196", "prompt_length": 958, "metadata": {"lesson_name": "观察植物细胞", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:39:06.944032"}}, "21009ab4e9430c93": {"lesson_id": "79af5fee198744d0bf05e0c07b1a53bd", "template_type": "complex", "file_path": "cache/complex_prompts/79af5fee198744d0bf05e0c07b1a53bd_complex_prompt.txt", "cached_at": "2025-07-13T10:39:13.137819", "prompt_length": 948, "metadata": {"lesson_name": "生态系统的信息传递", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:39:13.137657"}}, "8c144b806665119d": {"lesson_id": "ba397e402838413aaa9c0ab62d7c7cec", "template_type": "complex", "file_path": "cache/complex_prompts/ba397e402838413aaa9c0ab62d7c7cec_complex_prompt.txt", "cached_at": "2025-07-13T10:39:19.363056", "prompt_length": 931, "metadata": {"lesson_name": "孙权劝学", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:39:19.362896"}}, "832ff9b8650e8fcd": {"lesson_id": "3b650667aa374937811ce5cbbb7351ec", "template_type": "complex", "file_path": "cache/complex_prompts/3b650667aa374937811ce5cbbb7351ec_complex_prompt.txt", "cached_at": "2025-07-13T10:39:25.517923", "prompt_length": 1000, "metadata": {"lesson_name": "尊重自由平等", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:39:25.517754"}}, "1a4e44284cdbce70": {"lesson_id": "3f5a38ee51124d1faedaa4bb25b5fa8f", "template_type": "complex", "file_path": "cache/complex_prompts/3f5a38ee51124d1faedaa4bb25b5fa8f_complex_prompt.txt", "cached_at": "2025-07-13T10:39:31.742454", "prompt_length": 878, "metadata": {"lesson_name": "晚来天欲雪，能饮一杯无--白居易群诗阅读", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:39:31.742301"}}, "43ef44c107179a98": {"lesson_id": "ca8f84e3200f448a94f70479cd5482b7", "template_type": "complex", "file_path": "cache/complex_prompts/ca8f84e3200f448a94f70479cd5482b7_complex_prompt.txt", "cached_at": "2025-07-13T10:39:37.923554", "prompt_length": 902, "metadata": {"lesson_name": "欧洲西部", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:39:37.923399"}}, "fb0b3a9db299fe21": {"lesson_id": "31e6a8e3ca2f45e7bbe7075aad13dd45", "template_type": "complex", "file_path": "cache/complex_prompts/31e6a8e3ca2f45e7bbe7075aad13dd45_complex_prompt.txt", "cached_at": "2025-07-13T10:39:44.057823", "prompt_length": 954, "metadata": {"lesson_name": "聚落", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:39:44.057655"}}, "866312c653104de5": {"lesson_id": "6d628e50c5a64d0b9a57a2f3743f0427", "template_type": "complex", "file_path": "cache/complex_prompts/6d628e50c5a64d0b9a57a2f3743f0427_complex_prompt.txt", "cached_at": "2025-07-13T10:39:50.192896", "prompt_length": 857, "metadata": {"lesson_name": "slam与路径规划", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:39:50.192720"}}, "c1d41d4d09acb977": {"lesson_id": "b482cd3cb0dd439f8abf6c350789ec9c", "template_type": "complex", "file_path": "cache/complex_prompts/b482cd3cb0dd439f8abf6c350789ec9c_complex_prompt.txt", "cached_at": "2025-07-13T10:39:56.329031", "prompt_length": 856, "metadata": {"lesson_name": "分解因式", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:39:56.328854"}}, "e0a8b51e08fd9ebb": {"lesson_id": "c18332f4a539460db10100fccfc8bb20", "template_type": "complex", "file_path": "cache/complex_prompts/c18332f4a539460db10100fccfc8bb20_complex_prompt.txt", "cached_at": "2025-07-13T10:40:02.465822", "prompt_length": 943, "metadata": {"lesson_name": "物联网初体验", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:40:02.465669"}}, "b0f970e548404ff4": {"lesson_id": "af8687ac5fcc49319a2fed01150f96b9", "template_type": "complex", "file_path": "cache/complex_prompts/af8687ac5fcc49319a2fed01150f96b9_complex_prompt.txt", "cached_at": "2025-07-13T10:40:08.612076", "prompt_length": 958, "metadata": {"lesson_name": "工业区位条件", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:40:08.611909"}}, "689239513a7cb2b2": {"lesson_id": "d3dc767444d24376ae81c834a9fe4d85", "template_type": "complex", "file_path": "cache/complex_prompts/d3dc767444d24376ae81c834a9fe4d85_complex_prompt.txt", "cached_at": "2025-07-13T10:40:14.714394", "prompt_length": 1001, "metadata": {"lesson_name": "伟大的改革开放", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:40:14.714242"}}, "4414063e2805d572": {"lesson_id": "419c0846944a4cf48481432e9a078e96", "template_type": "complex", "file_path": "cache/complex_prompts/419c0846944a4cf48481432e9a078e96_complex_prompt.txt", "cached_at": "2025-07-13T10:40:20.831252", "prompt_length": 1000, "metadata": {"lesson_name": "平行四边形的面积", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:40:20.831075"}}, "aace4080f28a9a76": {"lesson_id": "3c8fd37726db4efab4ad9c6e4eb7a83e", "template_type": "complex", "file_path": "cache/complex_prompts/3c8fd37726db4efab4ad9c6e4eb7a83e_complex_prompt.txt", "cached_at": "2025-07-13T10:40:26.983818", "prompt_length": 908, "metadata": {"lesson_name": "专题复习《中国共产党的奋斗历程及建设成就》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:40:26.983656"}}, "9ea058b4617f55dc": {"lesson_id": "87ed6af103544b98b4b87f6ea01185e9", "template_type": "complex", "file_path": "cache/complex_prompts/87ed6af103544b98b4b87f6ea01185e9_complex_prompt.txt", "cached_at": "2025-07-13T10:40:33.143805", "prompt_length": 797, "metadata": {"lesson_name": "《拟人化的动漫形象》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:40:33.143039"}}, "86f78c93e98074d9": {"lesson_id": "dcfa6eddc94545c88e004ac9ca750b96", "template_type": "complex", "file_path": "cache/complex_prompts/dcfa6eddc94545c88e004ac9ca750b96_complex_prompt.txt", "cached_at": "2025-07-13T10:40:39.602713", "prompt_length": 999, "metadata": {"lesson_name": "什么是面积", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:40:39.602547"}}, "04373fbd42728c22": {"lesson_id": "42f0192783b44763b6adf1ba1deaec93", "template_type": "complex", "file_path": "cache/complex_prompts/42f0192783b44763b6adf1ba1deaec93_complex_prompt.txt", "cached_at": "2025-07-13T10:40:45.817806", "prompt_length": 885, "metadata": {"lesson_name": "ATP的主要来源-细胞呼吸", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:40:45.817653"}}, "9f47ba7c97c0510d": {"lesson_id": "5675d1f8501b40c586b76c856d12c59c", "template_type": "complex", "file_path": "cache/complex_prompts/5675d1f8501b40c586b76c856d12c59c_complex_prompt.txt", "cached_at": "2025-07-13T10:40:51.997949", "prompt_length": 961, "metadata": {"lesson_name": "成长的不仅仅是身体", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:40:51.997776"}}, "4bb768c0f8036d8c": {"lesson_id": "00b63245f4ae4a7f87c4a1f1df59b4be", "template_type": "complex", "file_path": "cache/complex_prompts/00b63245f4ae4a7f87c4a1f1df59b4be_complex_prompt.txt", "cached_at": "2025-07-13T10:40:58.180072", "prompt_length": 902, "metadata": {"lesson_name": "平移图形", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:40:58.179911"}}, "4304470cc36f1b0a": {"lesson_id": "8fd2b012c9fc48fdb2f03ae3b2f488d0", "template_type": "complex", "file_path": "cache/complex_prompts/8fd2b012c9fc48fdb2f03ae3b2f488d0_complex_prompt.txt", "cached_at": "2025-07-13T10:41:04.308128", "prompt_length": 754, "metadata": {"lesson_name": "9 中国有了共产党", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:41:04.307973"}}, "675199b2c2856013": {"lesson_id": "f37a5a4c369c4a0492763aaab5e237b8", "template_type": "complex", "file_path": "cache/complex_prompts/f37a5a4c369c4a0492763aaab5e237b8_complex_prompt.txt", "cached_at": "2025-07-13T10:41:10.372407", "prompt_length": 949, "metadata": {"lesson_name": "建筑结构及其简单设计", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:41:10.372248"}}, "d025eb8b6e03919b": {"lesson_id": "6562f70319504b3f810f806823d78c95", "template_type": "complex", "file_path": "cache/complex_prompts/6562f70319504b3f810f806823d78c95_complex_prompt.txt", "cached_at": "2025-07-13T10:41:16.430119", "prompt_length": 964, "metadata": {"lesson_name": "Lession29", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:41:16.429965"}}, "83bdbcd0cf0b4545": {"lesson_id": "d8d2bd8ef74c4f51964f421ff0993660", "template_type": "complex", "file_path": "cache/complex_prompts/d8d2bd8ef74c4f51964f421ff0993660_complex_prompt.txt", "cached_at": "2025-07-13T10:41:22.505295", "prompt_length": 976, "metadata": {"lesson_name": "第二次鸦片战争", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:41:22.505116"}}, "9831a912f2af1001": {"lesson_id": "730ef4dfbc3844ba82c8e907201cec86", "template_type": "complex", "file_path": "cache/complex_prompts/730ef4dfbc3844ba82c8e907201cec86_complex_prompt.txt", "cached_at": "2025-07-13T10:41:28.717881", "prompt_length": 944, "metadata": {"lesson_name": "圆周运动习题课", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:41:28.717715"}}, "a32a11200e5e7342": {"lesson_id": "8052f980916c4e609cf8d160eb36e209", "template_type": "complex", "file_path": "cache/complex_prompts/8052f980916c4e609cf8d160eb36e209_complex_prompt.txt", "cached_at": "2025-07-13T10:41:34.948791", "prompt_length": 959, "metadata": {"lesson_name": "狼牙山五壮士", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:41:34.948633"}}, "8b8cd856e7b4f60a": {"lesson_id": "4f33a5a5db3b4f4493fea6fd0ffa2049", "template_type": "complex", "file_path": "cache/complex_prompts/4f33a5a5db3b4f4493fea6fd0ffa2049_complex_prompt.txt", "cached_at": "2025-07-13T10:41:40.932662", "prompt_length": 945, "metadata": {"lesson_name": "奇妙的节日风俗", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:41:40.932508"}}, "49f6dc8d1040cc98": {"lesson_id": "dc60ab23d52044ca87dac65b3b19f2b2", "template_type": "complex", "file_path": "cache/complex_prompts/dc60ab23d52044ca87dac65b3b19f2b2_complex_prompt.txt", "cached_at": "2025-07-13T10:41:46.958079", "prompt_length": 907, "metadata": {"lesson_name": "甲烷，让生活更美好", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:41:46.957926"}}, "a4453648d91b106d": {"lesson_id": "fafe80047f854229839fb50a6d9a8710", "template_type": "complex", "file_path": "cache/complex_prompts/fafe80047f854229839fb50a6d9a8710_complex_prompt.txt", "cached_at": "2025-07-13T10:41:53.614111", "prompt_length": 943, "metadata": {"lesson_name": "从分数到分式", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:41:53.613938"}}, "83bc65527ebdebd5": {"lesson_id": "e579054e1992449a9d1ba51c764406e2", "template_type": "complex", "file_path": "cache/complex_prompts/e579054e1992449a9d1ba51c764406e2_complex_prompt.txt", "cached_at": "2025-07-13T10:41:59.798653", "prompt_length": 882, "metadata": {"lesson_name": "7.1自由平等的真谛", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:41:59.798484"}}, "f705e9a08739ba46": {"lesson_id": "b330efd70bb04d1baa5bfa440ddcdca3", "template_type": "complex", "file_path": "cache/complex_prompts/b330efd70bb04d1baa5bfa440ddcdca3_complex_prompt.txt", "cached_at": "2025-07-13T10:42:05.993564", "prompt_length": 959, "metadata": {"lesson_name": "第十三章 轴对称", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:42:05.993388"}}, "b368aab1895fa561": {"lesson_id": "b0553f971de3433188232297e803af0e", "template_type": "complex", "file_path": "cache/complex_prompts/b0553f971de3433188232297e803af0e_complex_prompt.txt", "cached_at": "2025-07-13T10:42:12.189732", "prompt_length": 954, "metadata": {"lesson_name": "推理", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:42:12.189564"}}, "a2e3482e8e13c7e6": {"lesson_id": "92b276669c4242a4a65b69db84c53964", "template_type": "complex", "file_path": "cache/complex_prompts/92b276669c4242a4a65b69db84c53964_complex_prompt.txt", "cached_at": "2025-07-13T10:42:18.433068", "prompt_length": 1030, "metadata": {"lesson_name": "Unit4 What's the best movie theater？", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:42:18.432904"}}, "2b1852e53a465152": {"lesson_id": "ce65cc5ec7914360804691a518ed4129", "template_type": "complex", "file_path": "cache/complex_prompts/ce65cc5ec7914360804691a518ed4129_complex_prompt.txt", "cached_at": "2025-07-13T10:42:24.693138", "prompt_length": 935, "metadata": {"lesson_name": "识字7 大小多少", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:42:24.692967"}}, "88c736496cbe7a48": {"lesson_id": "eceecc9adfab417c8ce326298f8c5982", "template_type": "complex", "file_path": "cache/complex_prompts/eceecc9adfab417c8ce326298f8c5982_complex_prompt.txt", "cached_at": "2025-07-13T10:42:30.971027", "prompt_length": 1003, "metadata": {"lesson_name": "第一单元 3.荷花", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:42:30.970859"}}, "45ef7dc9b87315be": {"lesson_id": "58b1ac9380684e2a8ed0a286618437a7", "template_type": "complex", "file_path": "cache/complex_prompts/58b1ac9380684e2a8ed0a286618437a7_complex_prompt.txt", "cached_at": "2025-07-13T10:42:37.205831", "prompt_length": 957, "metadata": {"lesson_name": "点亮小灯泡", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:42:37.205663"}}, "e5c99710875098a3": {"lesson_id": "85a9b900449347ddb54fa39b568a408c", "template_type": "complex", "file_path": "cache/complex_prompts/85a9b900449347ddb54fa39b568a408c_complex_prompt.txt", "cached_at": "2025-07-13T10:42:43.427740", "prompt_length": 959, "metadata": {"lesson_name": "圆中的最值问题", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:42:43.427581"}}, "b571e94f4bd1cdcc": {"lesson_id": "158b6df9c0d84c9d84e2ace4d2ff66c2", "template_type": "complex", "file_path": "cache/complex_prompts/158b6df9c0d84c9d84e2ace4d2ff66c2_complex_prompt.txt", "cached_at": "2025-07-13T10:42:49.713423", "prompt_length": 946, "metadata": {"lesson_name": "中国的地理差异", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:42:49.713258"}}, "31880a8da5bca011": {"lesson_id": "945906df2cf142a3afd3472e5c06daac", "template_type": "complex", "file_path": "cache/complex_prompts/945906df2cf142a3afd3472e5c06daac_complex_prompt.txt", "cached_at": "2025-07-13T10:42:55.988073", "prompt_length": 978, "metadata": {"lesson_name": "第八单元 少年闰土", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:42:55.987905"}}, "6d01fa6d6346905b": {"lesson_id": "40cf44f58dfb40df8d371b9f5971d6f3", "template_type": "complex", "file_path": "cache/complex_prompts/40cf44f58dfb40df8d371b9f5971d6f3_complex_prompt.txt", "cached_at": "2025-07-13T10:43:02.215921", "prompt_length": 957, "metadata": {"lesson_name": "unit 2 If you tell him the truth now，you'll show that you are honest.", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:43:02.215757"}}, "4d2ea3c0c0b3b665": {"lesson_id": "4079f0b9fa6540008a9d283f46b539ed", "template_type": "complex", "file_path": "cache/complex_prompts/4079f0b9fa6540008a9d283f46b539ed_complex_prompt.txt", "cached_at": "2025-07-13T10:43:08.452832", "prompt_length": 956, "metadata": {"lesson_name": "圆的认识", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:43:08.452667"}}, "56d93e692badb383": {"lesson_id": "5646f17a21cc49fdacb943a556b8d616", "template_type": "complex", "file_path": "cache/complex_prompts/5646f17a21cc49fdacb943a556b8d616_complex_prompt.txt", "cached_at": "2025-07-13T10:43:14.688713", "prompt_length": 956, "metadata": {"lesson_name": "《1.2 数字化与编码》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:43:14.688546"}}, "a4c2ed5d064f5727": {"lesson_id": "bdb6c3c9b8d24112bbd97724297cd408", "template_type": "complex", "file_path": "cache/complex_prompts/bdb6c3c9b8d24112bbd97724297cd408_complex_prompt.txt", "cached_at": "2025-07-13T10:43:20.958063", "prompt_length": 958, "metadata": {"lesson_name": "《预防传染病》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:43:20.957901"}}, "d0340981552c314f": {"lesson_id": "e9f2ddd69a14422dace6e244ddf334a5", "template_type": "complex", "file_path": "cache/complex_prompts/e9f2ddd69a14422dace6e244ddf334a5_complex_prompt.txt", "cached_at": "2025-07-13T10:43:27.227044", "prompt_length": 874, "metadata": {"lesson_name": "《东晋南朝时期江南地区的开发》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:43:27.226874"}}, "a5b882caf089f91f": {"lesson_id": "c2711dc5d37a4cb49ddb5d88f5f80f63", "template_type": "complex", "file_path": "cache/complex_prompts/c2711dc5d37a4cb49ddb5d88f5f80f63_complex_prompt.txt", "cached_at": "2025-07-13T10:43:33.627239", "prompt_length": 908, "metadata": {"lesson_name": "你好，压力“菌”", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:43:33.627074"}}, "1cfca6db656afcee": {"lesson_id": "e364e2b2fcdd4bfe8225ee84daeb1f01", "template_type": "complex", "file_path": "cache/complex_prompts/e364e2b2fcdd4bfe8225ee84daeb1f01_complex_prompt.txt", "cached_at": "2025-07-13T10:43:39.831348", "prompt_length": 904, "metadata": {"lesson_name": "开天辟地的大事", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-13T10:43:39.831171"}}, "2d109e98055f6ab3": {"lesson_id": "49f4252b5fb14f639191539502d29574", "template_type": "complex", "file_path": "cache/complex_prompts/49f4252b5fb14f639191539502d29574_complex_prompt.txt", "cached_at": "2025-07-25T00:44:55.232319", "prompt_length": 926, "metadata": {"lesson_name": "《Unit 2 Tintin has been popular for over eighty years》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:44:55.232167"}}, "ae5c84b3d4fd7e33": {"lesson_id": "fc36691e2f45459b83eab22f9ad13a0e", "template_type": "complex", "file_path": "cache/complex_prompts/fc36691e2f45459b83eab22f9ad13a0e_complex_prompt.txt", "cached_at": "2025-07-25T00:46:25.499155", "prompt_length": 956, "metadata": {"lesson_name": "13 猫", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:46:25.498945"}}, "c481cdc743e348af": {"lesson_id": "660673c31aa74c33a63c307f63ab072e", "template_type": "complex", "file_path": "cache/complex_prompts/660673c31aa74c33a63c307f63ab072e_complex_prompt.txt", "cached_at": "2025-07-25T00:46:27.554532", "prompt_length": 911, "metadata": {"lesson_name": "《大气受热过程与热力环流》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:46:27.554371"}}, "a9e9fc3a0f598d2f": {"lesson_id": "cf337e4ae25a49538cf7a36e30d0f063", "template_type": "complex", "file_path": "cache/complex_prompts/cf337e4ae25a49538cf7a36e30d0f063_complex_prompt.txt", "cached_at": "2025-07-25T00:46:29.622393", "prompt_length": 922, "metadata": {"lesson_name": "16.宇宙的另一边", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:46:29.622210"}}, "a85f03b959d64c2f": {"lesson_id": "4ffd592d618f455fa4f99d7022f782a6", "template_type": "complex", "file_path": "cache/complex_prompts/4ffd592d618f455fa4f99d7022f782a6_complex_prompt.txt", "cached_at": "2025-07-25T00:46:31.651284", "prompt_length": 879, "metadata": {"lesson_name": "清新空气是个宝", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:46:31.651120"}}, "d60aa44d841d11ee": {"lesson_id": "97332f675e5141eeb04429e8c6b78c81", "template_type": "complex", "file_path": "cache/complex_prompts/97332f675e5141eeb04429e8c6b78c81_complex_prompt.txt", "cached_at": "2025-07-25T00:46:33.800583", "prompt_length": 902, "metadata": {"lesson_name": "伯牙鼓琴", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:46:33.800413"}}, "216c2d7c8e608260": {"lesson_id": "00b685eb677747c0a2f7b69cacbbdeb4", "template_type": "complex", "file_path": "cache/complex_prompts/00b685eb677747c0a2f7b69cacbbdeb4_complex_prompt.txt", "cached_at": "2025-07-25T00:46:36.015492", "prompt_length": 930, "metadata": {"lesson_name": "导盲机器人-<PERSON><PERSON>机器人巡线避障", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:46:36.015314"}}, "7f64d355eaf191db": {"lesson_id": "6a4a77b9df6c44cd84d74d620551ca91", "template_type": "complex", "file_path": "cache/complex_prompts/6a4a77b9df6c44cd84d74d620551ca91_complex_prompt.txt", "cached_at": "2025-07-25T00:46:38.190308", "prompt_length": 952, "metadata": {"lesson_name": "高考基础语法突破篇形容词和副词", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:46:38.190124"}}, "8bfdf4b6c5692c26": {"lesson_id": "0e84bb0865c9468fa28d2f328caac2b2", "template_type": "complex", "file_path": "cache/complex_prompts/0e84bb0865c9468fa28d2f328caac2b2_complex_prompt.txt", "cached_at": "2025-07-25T00:46:40.195196", "prompt_length": 965, "metadata": {"lesson_name": "《平行四边形的性质》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:46:40.194955"}}, "8e0f0f564a8ac7e5": {"lesson_id": "f96d6742513b438fa85db1c552be965a", "template_type": "complex", "file_path": "cache/complex_prompts/f96d6742513b438fa85db1c552be965a_complex_prompt.txt", "cached_at": "2025-07-25T00:46:42.254567", "prompt_length": 885, "metadata": {"lesson_name": "8　数学广角──搭配（二）", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:46:42.254405"}}, "6875d95948ffe8f4": {"lesson_id": "17c3bfdd817a4483b9a0fc6de9e3b40b", "template_type": "complex", "file_path": "cache/complex_prompts/17c3bfdd817a4483b9a0fc6de9e3b40b_complex_prompt.txt", "cached_at": "2025-07-25T00:46:44.473268", "prompt_length": 879, "metadata": {"lesson_name": "组合图形的面积", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:46:44.473077"}}, "cc7392290e36dc5f": {"lesson_id": "d2ece2154c964cdba8ec15fb885b4c0e", "template_type": "complex", "file_path": "cache/complex_prompts/d2ece2154c964cdba8ec15fb885b4c0e_complex_prompt.txt", "cached_at": "2025-07-25T00:46:46.564004", "prompt_length": 915, "metadata": {"lesson_name": "《Working the land》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:46:46.563831"}}, "f9980ad8a195753b": {"lesson_id": "2b9bc4a43a1f4b13a4155193e1d2ffd0", "template_type": "complex", "file_path": "cache/complex_prompts/2b9bc4a43a1f4b13a4155193e1d2ffd0_complex_prompt.txt", "cached_at": "2025-07-25T00:46:48.680405", "prompt_length": 942, "metadata": {"lesson_name": "爬山虎的脚", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:46:48.680248"}}, "42f80985ffdbf3a5": {"lesson_id": "1443720e7eab424e81341136d2fa4420", "template_type": "complex", "file_path": "cache/complex_prompts/1443720e7eab424e81341136d2fa4420_complex_prompt.txt", "cached_at": "2025-07-25T00:46:50.923952", "prompt_length": 839, "metadata": {"lesson_name": "组合图形的面积", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:46:50.923786"}}, "8e1186823bfa7b99": {"lesson_id": "81e64fa1eebe436bbb3c780ad968d947", "template_type": "complex", "file_path": "cache/complex_prompts/81e64fa1eebe436bbb3c780ad968d947_complex_prompt.txt", "cached_at": "2025-07-25T00:46:53.024705", "prompt_length": 884, "metadata": {"lesson_name": "长方体和正方体的回顾整理", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:46:53.024547"}}, "7dfc370e7c117b8d": {"lesson_id": "de94d42fd58445feae02829ff3ec0e3c", "template_type": "complex", "file_path": "cache/complex_prompts/de94d42fd58445feae02829ff3ec0e3c_complex_prompt.txt", "cached_at": "2025-07-25T00:46:55.208115", "prompt_length": 895, "metadata": {"lesson_name": "长方形正方形面积", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:46:55.207956"}}, "9bfd768791bf6f84": {"lesson_id": "533728a556b844e29aa0f61a4e7f4008", "template_type": "complex", "file_path": "cache/complex_prompts/533728a556b844e29aa0f61a4e7f4008_complex_prompt.txt", "cached_at": "2025-07-25T00:46:57.211009", "prompt_length": 920, "metadata": {"lesson_name": "《网络新世界》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:46:57.210843"}}, "8b947a4d32fb8919": {"lesson_id": "e240955101bd49b0ba7bb36c0ba57fd4", "template_type": "complex", "file_path": "cache/complex_prompts/e240955101bd49b0ba7bb36c0ba57fd4_complex_prompt.txt", "cached_at": "2025-07-25T00:46:59.382683", "prompt_length": 901, "metadata": {"lesson_name": "篆刻艺术", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:46:59.382523"}}, "151f4f87b1c485c8": {"lesson_id": "03df8937d0e1425e9220cb35d2b84ccf", "template_type": "complex", "file_path": "cache/complex_prompts/03df8937d0e1425e9220cb35d2b84ccf_complex_prompt.txt", "cached_at": "2025-07-25T00:47:01.456241", "prompt_length": 959, "metadata": {"lesson_name": "《压强》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:01.456081"}}, "fc56cbdd7dd0644f": {"lesson_id": "290d2fb11274416c927571f4087ab424", "template_type": "complex", "file_path": "cache/complex_prompts/290d2fb11274416c927571f4087ab424_complex_prompt.txt", "cached_at": "2025-07-25T00:47:03.536635", "prompt_length": 869, "metadata": {"lesson_name": "民主决策-作出理性选择", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:03.536470"}}, "c4293f39377129a3": {"lesson_id": "c47bdfe1bb0c4a9181796697adb32f52", "template_type": "complex", "file_path": "cache/complex_prompts/c47bdfe1bb0c4a9181796697adb32f52_complex_prompt.txt", "cached_at": "2025-07-25T00:47:05.625060", "prompt_length": 1009, "metadata": {"lesson_name": "we love animals", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:05.624875"}}, "4fdc0df50c72fc02": {"lesson_id": "4826c46a47194bd28e89c350adf37a57", "template_type": "complex", "file_path": "cache/complex_prompts/4826c46a47194bd28e89c350adf37a57_complex_prompt.txt", "cached_at": "2025-07-25T00:47:07.676893", "prompt_length": 810, "metadata": {"lesson_name": "认识轴对称图形（平台-图形的运动）", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:07.676728"}}, "b9575791ccf8a568": {"lesson_id": "6c18b36558b74c4294e831ca62c6a4e9", "template_type": "complex", "file_path": "cache/complex_prompts/6c18b36558b74c4294e831ca62c6a4e9_complex_prompt.txt", "cached_at": "2025-07-25T00:47:09.752323", "prompt_length": 901, "metadata": {"lesson_name": "守株待兔", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:09.752130"}}, "9185272f0009f13b": {"lesson_id": "c72d141dcc054a22bd0bd0eb55258c99", "template_type": "complex", "file_path": "cache/complex_prompts/c72d141dcc054a22bd0bd0eb55258c99_complex_prompt.txt", "cached_at": "2025-07-25T00:47:11.916481", "prompt_length": 906, "metadata": {"lesson_name": "摸球游戏——可能性", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:11.916313"}}, "eed3364cd039c802": {"lesson_id": "a65fbec2362b48ceab301a6df2e6210c", "template_type": "complex", "file_path": "cache/complex_prompts/a65fbec2362b48ceab301a6df2e6210c_complex_prompt.txt", "cached_at": "2025-07-25T00:47:14.111347", "prompt_length": 918, "metadata": {"lesson_name": "爬山虎的脚", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:14.111168"}}, "6ecb35b0394a72d6": {"lesson_id": "d85f580e5ff6443fbca2e4b031f674db", "template_type": "complex", "file_path": "cache/complex_prompts/d85f580e5ff6443fbca2e4b031f674db_complex_prompt.txt", "cached_at": "2025-07-25T00:47:16.175669", "prompt_length": 837, "metadata": {"lesson_name": "世界的地形", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:16.175493"}}, "dd96237f596caa02": {"lesson_id": "8e37e4e03385414e9412071b66f86d65", "template_type": "complex", "file_path": "cache/complex_prompts/8e37e4e03385414e9412071b66f86d65_complex_prompt.txt", "cached_at": "2025-07-25T00:47:18.283110", "prompt_length": 918, "metadata": {"lesson_name": "<PERSON> was sitting with her sister by the river", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:18.282938"}}, "ae70c0e5054a9b8b": {"lesson_id": "837e7d7ea96e44febb158737c22f206b", "template_type": "complex", "file_path": "cache/complex_prompts/837e7d7ea96e44febb158737c22f206b_complex_prompt.txt", "cached_at": "2025-07-25T00:47:20.378578", "prompt_length": 836, "metadata": {"lesson_name": "数与形", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:20.378415"}}, "0918c0c9a1d10988": {"lesson_id": "0b984fd2980c4ca5ae6349141df40a3b", "template_type": "complex", "file_path": "cache/complex_prompts/0b984fd2980c4ca5ae6349141df40a3b_complex_prompt.txt", "cached_at": "2025-07-25T00:47:22.455068", "prompt_length": 935, "metadata": {"lesson_name": "第9课 辛亥革命", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:22.454897"}}, "56a045c8c3a238a9": {"lesson_id": "035c02f76b12433d8eab75c4c82095b9", "template_type": "complex", "file_path": "cache/complex_prompts/035c02f76b12433d8eab75c4c82095b9_complex_prompt.txt", "cached_at": "2025-07-25T00:47:24.564485", "prompt_length": 941, "metadata": {"lesson_name": "《春晓》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:24.564312"}}, "c256b4cb1dddedf2": {"lesson_id": "46e5da7b8fa64de48bd7080a62061650", "template_type": "complex", "file_path": "cache/complex_prompts/46e5da7b8fa64de48bd7080a62061650_complex_prompt.txt", "cached_at": "2025-07-25T00:47:26.638036", "prompt_length": 932, "metadata": {"lesson_name": "慧眼看交通", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:26.637856"}}, "5826bb29d04bfb50": {"lesson_id": "e8aed522bede462fbe7ebaf32b42697b", "template_type": "complex", "file_path": "cache/complex_prompts/e8aed522bede462fbe7ebaf32b42697b_complex_prompt.txt", "cached_at": "2025-07-25T00:47:28.693228", "prompt_length": 857, "metadata": {"lesson_name": "有趣的“龟兔赛跑”", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:28.693054"}}, "f07b2ec287160125": {"lesson_id": "b6c691651cb4409a9c03e9bf8f27dc29", "template_type": "complex", "file_path": "cache/complex_prompts/b6c691651cb4409a9c03e9bf8f27dc29_complex_prompt.txt", "cached_at": "2025-07-25T00:47:30.818485", "prompt_length": 864, "metadata": {"lesson_name": "平行线的性质", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:30.818307"}}, "28d17082049545d5": {"lesson_id": "a20c632931d24bcbaf849fc9e147ed60", "template_type": "complex", "file_path": "cache/complex_prompts/a20c632931d24bcbaf849fc9e147ed60_complex_prompt.txt", "cached_at": "2025-07-25T00:47:33.061899", "prompt_length": 943, "metadata": {"lesson_name": "制作创意小船", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:33.061722"}}, "427d38fd27f81ea9": {"lesson_id": "3497aca84d054dfcb62c3793791b2814", "template_type": "complex", "file_path": "cache/complex_prompts/3497aca84d054dfcb62c3793791b2814_complex_prompt.txt", "cached_at": "2025-07-25T00:47:35.141082", "prompt_length": 946, "metadata": {"lesson_name": "《负数的初步认识》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:35.140906"}}, "4d13ebf0e370982b": {"lesson_id": "6240f2d85fe24b77a726a487dad1f260", "template_type": "complex", "file_path": "cache/complex_prompts/6240f2d85fe24b77a726a487dad1f260_complex_prompt.txt", "cached_at": "2025-07-25T00:47:37.298104", "prompt_length": 908, "metadata": {"lesson_name": "声现象在科技中的应用", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:37.297935"}}, "3932145d62d9ff51": {"lesson_id": "a1e101340204460ab23e8c14706133a0", "template_type": "complex", "file_path": "cache/complex_prompts/a1e101340204460ab23e8c14706133a0_complex_prompt.txt", "cached_at": "2025-07-25T00:47:39.525707", "prompt_length": 969, "metadata": {"lesson_name": "《基于“好分数”的月测评讲》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:39.525536"}}, "b9722194fff779ab": {"lesson_id": "2cf7bdf1603647bebdd3094ad6b02d19", "template_type": "complex", "file_path": "cache/complex_prompts/2cf7bdf1603647bebdd3094ad6b02d19_complex_prompt.txt", "cached_at": "2025-07-25T00:47:41.738434", "prompt_length": 959, "metadata": {"lesson_name": "《9古诗三首》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:41.738263"}}, "9a8211a1f4fb5ff9": {"lesson_id": "1e8ce5dca7b3424687876596d8b2cee0", "template_type": "complex", "file_path": "cache/complex_prompts/1e8ce5dca7b3424687876596d8b2cee0_complex_prompt.txt", "cached_at": "2025-07-25T00:47:43.972930", "prompt_length": 892, "metadata": {"lesson_name": "认识图形", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:43.972762"}}, "fc514b8a525e5d94": {"lesson_id": "ba47514468d34af7a99eb8aa03f2d854", "template_type": "complex", "file_path": "cache/complex_prompts/ba47514468d34af7a99eb8aa03f2d854_complex_prompt.txt", "cached_at": "2025-07-25T00:47:46.085783", "prompt_length": 881, "metadata": {"lesson_name": "《4.昆虫备忘录》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:46.085622"}}, "8adffa4fb898d3f2": {"lesson_id": "e127009c3d804ea4a80a8bec13bf5f56", "template_type": "complex", "file_path": "cache/complex_prompts/e127009c3d804ea4a80a8bec13bf5f56_complex_prompt.txt", "cached_at": "2025-07-25T00:47:48.142417", "prompt_length": 837, "metadata": {"lesson_name": "牧民新歌", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:48.142246"}}, "f93fe15aa8f8e1ca": {"lesson_id": "8de1a8d7c1b2447ca0efc1fa6bd4f7c4", "template_type": "complex", "file_path": "cache/complex_prompts/8de1a8d7c1b2447ca0efc1fa6bd4f7c4_complex_prompt.txt", "cached_at": "2025-07-25T00:47:50.347478", "prompt_length": 864, "metadata": {"lesson_name": "10 爬山虎的脚", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:50.347289"}}, "2be1eadcdd0a93b2": {"lesson_id": "34420daaea644c248ee9c7e687e3cafc", "template_type": "complex", "file_path": "cache/complex_prompts/34420daaea644c248ee9c7e687e3cafc_complex_prompt.txt", "cached_at": "2025-07-25T00:47:52.432981", "prompt_length": 907, "metadata": {"lesson_name": "18富饶的西沙群岛", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:52.432798"}}, "719d0dc544b42052": {"lesson_id": "3a671f80b55340e3ae185ab986682e44", "template_type": "complex", "file_path": "cache/complex_prompts/3a671f80b55340e3ae185ab986682e44_complex_prompt.txt", "cached_at": "2025-07-25T00:47:54.519022", "prompt_length": 884, "metadata": {"lesson_name": "3.2立体几何中的向量法", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:54.518849"}}, "f6e18b186ae88aa2": {"lesson_id": "bcc9e3e4bf464628bb14b2b136b9d608", "template_type": "complex", "file_path": "cache/complex_prompts/bcc9e3e4bf464628bb14b2b136b9d608_complex_prompt.txt", "cached_at": "2025-07-25T00:47:56.579414", "prompt_length": 947, "metadata": {"lesson_name": "平行与相交整理复习课", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:56.579245"}}, "9339d5fcabc7ba15": {"lesson_id": "73968fe6b75240ca85be26e958f7dd0a", "template_type": "complex", "file_path": "cache/complex_prompts/73968fe6b75240ca85be26e958f7dd0a_complex_prompt.txt", "cached_at": "2025-07-25T00:47:58.634371", "prompt_length": 996, "metadata": {"lesson_name": "6.3 三角形的中位线", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:47:58.634207"}}, "1df571d32322e3c3": {"lesson_id": "f5ce4cfa12394754988b06f73f924166", "template_type": "complex", "file_path": "cache/complex_prompts/f5ce4cfa12394754988b06f73f924166_complex_prompt.txt", "cached_at": "2025-07-25T00:48:00.853966", "prompt_length": 950, "metadata": {"lesson_name": "《求一个数的百分之几》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:00.853789"}}, "6fb7f7195b61be9f": {"lesson_id": "725c68376e4a408a987728eb5c8218ff", "template_type": "complex", "file_path": "cache/complex_prompts/725c68376e4a408a987728eb5c8218ff_complex_prompt.txt", "cached_at": "2025-07-25T00:48:02.908478", "prompt_length": 905, "metadata": {"lesson_name": "位置与方向（二）", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:02.908304"}}, "75f88d09e889364b": {"lesson_id": "986b7bd9d6b44d7089cfe9743ab85f17", "template_type": "complex", "file_path": "cache/complex_prompts/986b7bd9d6b44d7089cfe9743ab85f17_complex_prompt.txt", "cached_at": "2025-07-25T00:48:05.212836", "prompt_length": 903, "metadata": {"lesson_name": "完全平方公式", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:05.212664"}}, "8aee20cd0529354c": {"lesson_id": "396652f73a9448f89884541b68c41584", "template_type": "complex", "file_path": "cache/complex_prompts/396652f73a9448f89884541b68c41584_complex_prompt.txt", "cached_at": "2025-07-25T00:48:07.385603", "prompt_length": 840, "metadata": {"lesson_name": "《巧用对称图形》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:07.385440"}}, "08fab18a0a865d05": {"lesson_id": "4b4a1e7920704a608beb5693c6d1e717", "template_type": "complex", "file_path": "cache/complex_prompts/4b4a1e7920704a608beb5693c6d1e717_complex_prompt.txt", "cached_at": "2025-07-25T00:48:09.467037", "prompt_length": 855, "metadata": {"lesson_name": "印象派的画家们", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:09.466857"}}, "ebb28a16427c7e10": {"lesson_id": "917e653aea9741e3a7b2d3347c10549a", "template_type": "complex", "file_path": "cache/complex_prompts/917e653aea9741e3a7b2d3347c10549a_complex_prompt.txt", "cached_at": "2025-07-25T00:48:11.516605", "prompt_length": 999, "metadata": {"lesson_name": "3 图形与变换", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:11.516415"}}, "80f57575aa75877f": {"lesson_id": "0cb5ff4c01224ca29ec8eb9e0d45cb70", "template_type": "complex", "file_path": "cache/complex_prompts/0cb5ff4c01224ca29ec8eb9e0d45cb70_complex_prompt.txt", "cached_at": "2025-07-25T00:48:13.694523", "prompt_length": 898, "metadata": {"lesson_name": "《Unit 5 This is my family》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:13.694346"}}, "5e48f874d2ef3207": {"lesson_id": "25cfce21698c4b6ba2067ddf334dfc7b", "template_type": "complex", "file_path": "cache/complex_prompts/25cfce21698c4b6ba2067ddf334dfc7b_complex_prompt.txt", "cached_at": "2025-07-25T00:48:15.882732", "prompt_length": 877, "metadata": {"lesson_name": "我有抗逆力", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:15.882556"}}, "36cd4903e61f3a42": {"lesson_id": "ebffb4c020d94083a6d3204cefe3e46f", "template_type": "complex", "file_path": "cache/complex_prompts/ebffb4c020d94083a6d3204cefe3e46f_complex_prompt.txt", "cached_at": "2025-07-25T00:48:18.333053", "prompt_length": 953, "metadata": {"lesson_name": "猫", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:18.332867"}}, "9114f90f43f4b9b5": {"lesson_id": "20b516b310ea4f8fb77b37705c47b833", "template_type": "complex", "file_path": "cache/complex_prompts/20b516b310ea4f8fb77b37705c47b833_complex_prompt.txt", "cached_at": "2025-07-25T00:48:20.424778", "prompt_length": 908, "metadata": {"lesson_name": "过氧化钠性质实验探究", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:20.424600"}}, "c3bdd8b11c524756": {"lesson_id": "db6beecfd01b42b69235d07d20dc8f5a", "template_type": "complex", "file_path": "cache/complex_prompts/db6beecfd01b42b69235d07d20dc8f5a_complex_prompt.txt", "cached_at": "2025-07-25T00:48:22.484183", "prompt_length": 956, "metadata": {"lesson_name": "13 猫", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:22.484004"}}, "a404604917918615": {"lesson_id": "37f5fa843d4d4ebcb938e729906c8815", "template_type": "complex", "file_path": "cache/complex_prompts/37f5fa843d4d4ebcb938e729906c8815_complex_prompt.txt", "cached_at": "2025-07-25T00:48:24.536486", "prompt_length": 949, "metadata": {"lesson_name": "真理诞生于一百个问号之后", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:24.536326"}}, "38734b4e7d23ae94": {"lesson_id": "c7ccb4a0e55440b5a83a8edd14838a20", "template_type": "complex", "file_path": "cache/complex_prompts/c7ccb4a0e55440b5a83a8edd14838a20_complex_prompt.txt", "cached_at": "2025-07-25T00:48:26.609167", "prompt_length": 908, "metadata": {"lesson_name": "《4.买东西的学问》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:26.608336"}}, "870cb711873bf9de": {"lesson_id": "b90d022d712c4de8b3b9ff02e1255af1", "template_type": "complex", "file_path": "cache/complex_prompts/b90d022d712c4de8b3b9ff02e1255af1_complex_prompt.txt", "cached_at": "2025-07-25T00:48:28.699505", "prompt_length": 933, "metadata": {"lesson_name": "探访古代文明", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:28.699346"}}, "11758125adc57194": {"lesson_id": "a5780baa677c4934b23ad9ccef0953af", "template_type": "complex", "file_path": "cache/complex_prompts/a5780baa677c4934b23ad9ccef0953af_complex_prompt.txt", "cached_at": "2025-07-25T00:48:30.754892", "prompt_length": 946, "metadata": {"lesson_name": "整十、整百数的加减", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:30.754726"}}, "caf23ac1d183edc2": {"lesson_id": "af29b8fb3b384688bb153a3e22fd953b", "template_type": "complex", "file_path": "cache/complex_prompts/af29b8fb3b384688bb153a3e22fd953b_complex_prompt.txt", "cached_at": "2025-07-25T00:48:32.840377", "prompt_length": 900, "metadata": {"lesson_name": "图形的运动（一）平移和旋转", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:32.840206"}}, "e57ee1d15fe9c42b": {"lesson_id": "a9ce816cd2fb4a49a64d592186c829e0", "template_type": "complex", "file_path": "cache/complex_prompts/a9ce816cd2fb4a49a64d592186c829e0_complex_prompt.txt", "cached_at": "2025-07-25T00:48:35.017012", "prompt_length": 947, "metadata": {"lesson_name": "里面是怎样连接的", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:35.016834"}}, "2e32789906a3dcc7": {"lesson_id": "ae4e494bc36542adacde89de76bd5a32", "template_type": "complex", "file_path": "cache/complex_prompts/ae4e494bc36542adacde89de76bd5a32_complex_prompt.txt", "cached_at": "2025-07-25T00:48:37.256139", "prompt_length": 902, "metadata": {"lesson_name": "彝族舞曲", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:37.255918"}}, "3542745705640013": {"lesson_id": "3fa3c4b333454fd8966feb18fcef35fd", "template_type": "complex", "file_path": "cache/complex_prompts/3fa3c4b333454fd8966feb18fcef35fd_complex_prompt.txt", "cached_at": "2025-07-25T00:48:39.461156", "prompt_length": 962, "metadata": {"lesson_name": "“重复”的奥妙", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:39.460982"}}, "09ac8d34f13fbed7": {"lesson_id": "24367ee83b6648a6bee4c8c15959cbfa", "template_type": "complex", "file_path": "cache/complex_prompts/24367ee83b6648a6bee4c8c15959cbfa_complex_prompt.txt", "cached_at": "2025-07-25T00:48:41.645703", "prompt_length": 913, "metadata": {"lesson_name": "What can you do?", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:41.645524"}}, "e8596ef3dbb882d1": {"lesson_id": "ad2a34c5f6f54030a1431c7e43319ed8", "template_type": "complex", "file_path": "cache/complex_prompts/ad2a34c5f6f54030a1431c7e43319ed8_complex_prompt.txt", "cached_at": "2025-07-25T00:48:43.766768", "prompt_length": 870, "metadata": {"lesson_name": "中国传统建筑与与园林艺术", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:43.766598"}}, "ad17ee925c6d52b0": {"lesson_id": "0e2e2a7a148c486c88a10d9f7635b621", "template_type": "complex", "file_path": "cache/complex_prompts/0e2e2a7a148c486c88a10d9f7635b621_complex_prompt.txt", "cached_at": "2025-07-25T00:48:45.894764", "prompt_length": 863, "metadata": {"lesson_name": "腐乳的制作", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:45.894590"}}, "f5e12226d1f38b34": {"lesson_id": "e1180b5c6c5d4d3a88c489b445509d4e", "template_type": "complex", "file_path": "cache/complex_prompts/e1180b5c6c5d4d3a88c489b445509d4e_complex_prompt.txt", "cached_at": "2025-07-25T00:48:48.165906", "prompt_length": 933, "metadata": {"lesson_name": "用字母表示数", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:48.165745"}}, "af2f496ee0305b59": {"lesson_id": "d3a6266acb624876b110dae4ee7d9884", "template_type": "complex", "file_path": "cache/complex_prompts/d3a6266acb624876b110dae4ee7d9884_complex_prompt.txt", "cached_at": "2025-07-25T00:48:50.193082", "prompt_length": 909, "metadata": {"lesson_name": "魏晋南北朝的科技与文化", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:50.192913"}}, "e520f9eefa495814": {"lesson_id": "93b1053d6feb42a9853261e2c70f4d7c", "template_type": "complex", "file_path": "cache/complex_prompts/93b1053d6feb42a9853261e2c70f4d7c_complex_prompt.txt", "cached_at": "2025-07-25T00:48:52.259243", "prompt_length": 897, "metadata": {"lesson_name": "How ofen do you exercise？", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:52.259066"}}, "26e336d9346ae79d": {"lesson_id": "ad0ecc385ae04eeb97acb3cc2b29120b", "template_type": "complex", "file_path": "cache/complex_prompts/ad0ecc385ae04eeb97acb3cc2b29120b_complex_prompt.txt", "cached_at": "2025-07-25T00:48:54.314612", "prompt_length": 862, "metadata": {"lesson_name": "《桃花源记》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:54.314434"}}, "d8cb99e23cd521b2": {"lesson_id": "5dbd2510d23040059fa83229ade9ac32", "template_type": "complex", "file_path": "cache/complex_prompts/5dbd2510d23040059fa83229ade9ac32_complex_prompt.txt", "cached_at": "2025-07-25T00:48:56.622793", "prompt_length": 909, "metadata": {"lesson_name": "分子和原子性质的在研究", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:56.622624"}}, "c9a6578f822a9512": {"lesson_id": "7644bcc9d2234741b1ef244a353186ba", "template_type": "complex", "file_path": "cache/complex_prompts/7644bcc9d2234741b1ef244a353186ba_complex_prompt.txt", "cached_at": "2025-07-25T00:48:58.755532", "prompt_length": 970, "metadata": {"lesson_name": "Welcome to Hainan", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:48:58.755358"}}, "353d432d80df38b2": {"lesson_id": "02e9b218b64f4e12a574b99b1431715d", "template_type": "complex", "file_path": "cache/complex_prompts/02e9b218b64f4e12a574b99b1431715d_complex_prompt.txt", "cached_at": "2025-07-25T00:49:00.828737", "prompt_length": 990, "metadata": {"lesson_name": "Unit4 I used to be afriaid of the dark（SectionA1a-2c）", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:00.828562"}}, "30173dfc578e0d6b": {"lesson_id": "da365341a090434f9bcb061f9cdc919c", "template_type": "complex", "file_path": "cache/complex_prompts/da365341a090434f9bcb061f9cdc919c_complex_prompt.txt", "cached_at": "2025-07-25T00:49:02.950042", "prompt_length": 897, "metadata": {"lesson_name": "Unit1 It's big and light.", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:02.949868"}}, "03fd847f0888cdd6": {"lesson_id": "af9ac10e5c744e9fbc371a5b8caf9471", "template_type": "complex", "file_path": "cache/complex_prompts/af9ac10e5c744e9fbc371a5b8caf9471_complex_prompt.txt", "cached_at": "2025-07-25T00:49:05.081668", "prompt_length": 939, "metadata": {"lesson_name": "周长", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:05.081485"}}, "ec64cfcb8c521268": {"lesson_id": "4e54e446fdc44b04b01fa61d6a0d592f", "template_type": "complex", "file_path": "cache/complex_prompts/4e54e446fdc44b04b01fa61d6a0d592f_complex_prompt.txt", "cached_at": "2025-07-25T00:49:07.150413", "prompt_length": 855, "metadata": {"lesson_name": "《lesson12 Danny's Plant》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:07.150236"}}, "b2dbcd0d07c86b60": {"lesson_id": "b3e13a3e3a4d44439745da3edf95af45", "template_type": "complex", "file_path": "cache/complex_prompts/b3e13a3e3a4d44439745da3edf95af45_complex_prompt.txt", "cached_at": "2025-07-25T00:49:09.245676", "prompt_length": 864, "metadata": {"lesson_name": "一　减数分裂", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:09.245504"}}, "6264d584b1a06985": {"lesson_id": "4689473354894ed1a37ba857c86fefea", "template_type": "complex", "file_path": "cache/complex_prompts/4689473354894ed1a37ba857c86fefea_complex_prompt.txt", "cached_at": "2025-07-25T00:49:11.324970", "prompt_length": 902, "metadata": {"lesson_name": "认识周长", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:11.324791"}}, "d3ba3e0b47cd0bd8": {"lesson_id": "58471248d3a84cb6aa2bb925c1f08af6", "template_type": "complex", "file_path": "cache/complex_prompts/58471248d3a84cb6aa2bb925c1f08af6_complex_prompt.txt", "cached_at": "2025-07-25T00:49:13.620287", "prompt_length": 894, "metadata": {"lesson_name": "写作 抓住细节", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:13.620108"}}, "ebb2d7022624d171": {"lesson_id": "b663b94395e3433f9a821e06327b0e7c", "template_type": "complex", "file_path": "cache/complex_prompts/b663b94395e3433f9a821e06327b0e7c_complex_prompt.txt", "cached_at": "2025-07-25T00:49:15.711706", "prompt_length": 906, "metadata": {"lesson_name": "正面助跑屈腿跳高", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:15.711541"}}, "51d93cba2333c635": {"lesson_id": "dbe80a1c79554eb68118fc3fa5b5e81d", "template_type": "complex", "file_path": "cache/complex_prompts/dbe80a1c79554eb68118fc3fa5b5e81d_complex_prompt.txt", "cached_at": "2025-07-25T00:49:17.930730", "prompt_length": 912, "metadata": {"lesson_name": "逛超市-百以内进位加法的笔算", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:17.930557"}}, "7ffc37e37d4fbfa8": {"lesson_id": "ec5b590d38f145a5b196e80761db17fd", "template_type": "complex", "file_path": "cache/complex_prompts/ec5b590d38f145a5b196e80761db17fd_complex_prompt.txt", "cached_at": "2025-07-25T00:49:19.960714", "prompt_length": 960, "metadata": {"lesson_name": "大家的“朋友”", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:19.960550"}}, "1b02eecee0c3e07f": {"lesson_id": "ead43beae8cc4de4ab906efa2e25520a", "template_type": "complex", "file_path": "cache/complex_prompts/ead43beae8cc4de4ab906efa2e25520a_complex_prompt.txt", "cached_at": "2025-07-25T00:49:22.178214", "prompt_length": 967, "metadata": {"lesson_name": "8.4 三元一次方程组的解法", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:22.178040"}}, "f826a62cd0470bca": {"lesson_id": "41e52c555d2440279ac08a0d69e84ff8", "template_type": "complex", "file_path": "cache/complex_prompts/41e52c555d2440279ac08a0d69e84ff8_complex_prompt.txt", "cached_at": "2025-07-25T00:49:24.253523", "prompt_length": 881, "metadata": {"lesson_name": "数学广角--数与形", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:24.253353"}}, "ff8927f41b3ce26b": {"lesson_id": "4bcb5b2b6b7f4811bcb1ad93aa1d4420", "template_type": "complex", "file_path": "cache/complex_prompts/4bcb5b2b6b7f4811bcb1ad93aa1d4420_complex_prompt.txt", "cached_at": "2025-07-25T00:49:26.361521", "prompt_length": 1005, "metadata": {"lesson_name": "《第七单元 语文园地》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:26.361342"}}, "dc165cf67c565bc6": {"lesson_id": "06b116e156854fad84c73d8de67fd35c", "template_type": "complex", "file_path": "cache/complex_prompts/06b116e156854fad84c73d8de67fd35c_complex_prompt.txt", "cached_at": "2025-07-25T00:49:28.479971", "prompt_length": 841, "metadata": {"lesson_name": "有多少浪费本可避免", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:28.479798"}}, "b46ada4529d342e0": {"lesson_id": "9b7ba2d08280481e908c422c6e4e3752", "template_type": "complex", "file_path": "cache/complex_prompts/9b7ba2d08280481e908c422c6e4e3752_complex_prompt.txt", "cached_at": "2025-07-25T00:49:30.635708", "prompt_length": 971, "metadata": {"lesson_name": "Unit5 Dinner's ready! A.Let's talk", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:30.635551"}}, "08a945a129d07454": {"lesson_id": "6123641c64f9431581a2ff2478cebfae", "template_type": "complex", "file_path": "cache/complex_prompts/6123641c64f9431581a2ff2478cebfae_complex_prompt.txt", "cached_at": "2025-07-25T00:49:32.765144", "prompt_length": 948, "metadata": {"lesson_name": "What would you like Part A Let's learn & Role-play", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:32.764963"}}, "4e4b0234771a61d9": {"lesson_id": "af8559a760f7494382f9bf422eb27688", "template_type": "complex", "file_path": "cache/complex_prompts/af8559a760f7494382f9bf422eb27688_complex_prompt.txt", "cached_at": "2025-07-25T00:49:34.815019", "prompt_length": 979, "metadata": {"lesson_name": "will people have robots？", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:34.814832"}}, "0286c2facff91609": {"lesson_id": "c9d0f0e6f1314456b4e444b4875d53fc", "template_type": "complex", "file_path": "cache/complex_prompts/c9d0f0e6f1314456b4e444b4875d53fc_complex_prompt.txt", "cached_at": "2025-07-25T00:49:36.997089", "prompt_length": 902, "metadata": {"lesson_name": "智慧广场", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:36.996906"}}, "23caadd7d76f4837": {"lesson_id": "e0a9564d29304defa36be1971f3b9edd", "template_type": "complex", "file_path": "cache/complex_prompts/e0a9564d29304defa36be1971f3b9edd_complex_prompt.txt", "cached_at": "2025-07-25T00:49:39.083821", "prompt_length": 945, "metadata": {"lesson_name": "《经济体制改革》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:39.083648"}}, "0562ddf4308ef380": {"lesson_id": "42bd7f694c934a788904b5302e7d4a3d", "template_type": "complex", "file_path": "cache/complex_prompts/42bd7f694c934a788904b5302e7d4a3d_complex_prompt.txt", "cached_at": "2025-07-25T00:49:41.530917", "prompt_length": 1002, "metadata": {"lesson_name": "一、简单磁效应", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:41.530763"}}, "877805e08890dcdc": {"lesson_id": "f304f54e9a0c46248add22e81ccbb33b", "template_type": "complex", "file_path": "cache/complex_prompts/f304f54e9a0c46248add22e81ccbb33b_complex_prompt.txt", "cached_at": "2025-07-25T00:49:43.481580", "prompt_length": 944, "metadata": {"lesson_name": "四通八达的交通", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:43.481420"}}, "f567353c77e1774b": {"lesson_id": "d86366ba95dd43f6a0e19abb7f62133d", "template_type": "complex", "file_path": "cache/complex_prompts/d86366ba95dd43f6a0e19abb7f62133d_complex_prompt.txt", "cached_at": "2025-07-25T00:49:45.701769", "prompt_length": 988, "metadata": {"lesson_name": "数学广场-周期问题", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:45.701593"}}, "ca543096f0e1cdd8": {"lesson_id": "2aaca64c61494dd28e0aecae0089b496", "template_type": "complex", "file_path": "cache/complex_prompts/2aaca64c61494dd28e0aecae0089b496_complex_prompt.txt", "cached_at": "2025-07-25T00:49:47.776997", "prompt_length": 988, "metadata": {"lesson_name": "《大自然，谢谢您》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:47.776827"}}, "7c9cfd5d57cb20c6": {"lesson_id": "c2815778b0294e8db28ba787c6b91b4f", "template_type": "complex", "file_path": "cache/complex_prompts/c2815778b0294e8db28ba787c6b91b4f_complex_prompt.txt", "cached_at": "2025-07-25T00:49:50.014890", "prompt_length": 877, "metadata": {"lesson_name": "我是一张纸", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:50.014719"}}, "81769b3bb3a416c7": {"lesson_id": "ccafbc1dc155453f90f46d567e1d598b", "template_type": "complex", "file_path": "cache/complex_prompts/ccafbc1dc155453f90f46d567e1d598b_complex_prompt.txt", "cached_at": "2025-07-25T00:49:52.346267", "prompt_length": 837, "metadata": {"lesson_name": "《认识小数》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:52.346103"}}, "70378af5b2cd25c5": {"lesson_id": "fdd545c17f174815928fefab46894dc3", "template_type": "complex", "file_path": "cache/complex_prompts/fdd545c17f174815928fefab46894dc3_complex_prompt.txt", "cached_at": "2025-07-25T00:49:54.446734", "prompt_length": 937, "metadata": {"lesson_name": "一次函数的图像与性质", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:54.446568"}}, "11bfea972ee29cbd": {"lesson_id": "a1466ce04fb0406bb89c380969171a0d", "template_type": "complex", "file_path": "cache/complex_prompts/a1466ce04fb0406bb89c380969171a0d_complex_prompt.txt", "cached_at": "2025-07-25T00:49:56.574779", "prompt_length": 906, "metadata": {"lesson_name": "人体内废物的排出", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:56.574602"}}, "b386467160e40464": {"lesson_id": "f33b4edf1062467aa8db8135232148b2", "template_type": "complex", "file_path": "cache/complex_prompts/f33b4edf1062467aa8db8135232148b2_complex_prompt.txt", "cached_at": "2025-07-25T00:49:58.642448", "prompt_length": 853, "metadata": {"lesson_name": "搭配的学问", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:49:58.642269"}}, "6c6db25a39f295ca": {"lesson_id": "e44af6b12a214c2787ffd6efba4d2167", "template_type": "complex", "file_path": "cache/complex_prompts/e44af6b12a214c2787ffd6efba4d2167_complex_prompt.txt", "cached_at": "2025-07-25T00:50:00.695520", "prompt_length": 944, "metadata": {"lesson_name": "《演唱 樱花》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:00.695348"}}, "43e30165618fea93": {"lesson_id": "5cdbcd8e6cf444e6b3c3c5cc3f0c920b", "template_type": "complex", "file_path": "cache/complex_prompts/5cdbcd8e6cf444e6b3c3c5cc3f0c920b_complex_prompt.txt", "cached_at": "2025-07-25T00:50:02.754296", "prompt_length": 942, "metadata": {"lesson_name": "迷路的蜗牛", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:02.754097"}}, "0c89b7e09e05f4e8": {"lesson_id": "cc4aab2d15e04a908de8a143150b01f9", "template_type": "complex", "file_path": "cache/complex_prompts/cc4aab2d15e04a908de8a143150b01f9_complex_prompt.txt", "cached_at": "2025-07-25T00:50:04.843487", "prompt_length": 905, "metadata": {"lesson_name": "《人类的朋友》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:04.843309"}}, "4cf3cd68fb8559e7": {"lesson_id": "16c865aa4f324749bd6c4688a282039b", "template_type": "complex", "file_path": "cache/complex_prompts/16c865aa4f324749bd6c4688a282039b_complex_prompt.txt", "cached_at": "2025-07-25T00:50:06.927023", "prompt_length": 901, "metadata": {"lesson_name": "认识浮力", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:06.926832"}}, "25ca3d9d683bd4a5": {"lesson_id": "ca9c01f587114cb396453421acca5443", "template_type": "complex", "file_path": "cache/complex_prompts/ca9c01f587114cb396453421acca5443_complex_prompt.txt", "cached_at": "2025-07-25T00:50:09.014938", "prompt_length": 948, "metadata": {"lesson_name": "《多种多样的植物》平台为《形形色色的植物》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:09.014750"}}, "850b4a583ce95376": {"lesson_id": "5e98cbab0a564af4a5a43c4abe7f8ead", "template_type": "complex", "file_path": "cache/complex_prompts/5e98cbab0a564af4a5a43c4abe7f8ead_complex_prompt.txt", "cached_at": "2025-07-25T00:50:11.114438", "prompt_length": 908, "metadata": {"lesson_name": "有趣的图形-认识图形", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:11.114286"}}, "c3ae95f7acadcf54": {"lesson_id": "2c71810ac97941008645e697a7a8bb8b", "template_type": "complex", "file_path": "cache/complex_prompts/2c71810ac97941008645e697a7a8bb8b_complex_prompt.txt", "cached_at": "2025-07-25T00:50:13.175106", "prompt_length": 1052, "metadata": {"lesson_name": "Be helpful at home", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:13.174932"}}, "50ef5eafac9ec27c": {"lesson_id": "1f4df1e7fdaa41d9ae263fe8cf634e33", "template_type": "complex", "file_path": "cache/complex_prompts/1f4df1e7fdaa41d9ae263fe8cf634e33_complex_prompt.txt", "cached_at": "2025-07-25T00:50:15.346579", "prompt_length": 944, "metadata": {"lesson_name": "《北京的春节》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:15.346403"}}, "ae1a7c59c4ce3d48": {"lesson_id": "a6f72c4bd129461eac997a362f3b8047", "template_type": "complex", "file_path": "cache/complex_prompts/a6f72c4bd129461eac997a362f3b8047_complex_prompt.txt", "cached_at": "2025-07-25T00:50:17.465378", "prompt_length": 930, "metadata": {"lesson_name": "Unit4 At the farm partA  Let's learn &chant", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:17.465203"}}, "fc33e317de1a5649": {"lesson_id": "12a9940f3c2942ed9f0796bda4ab8697", "template_type": "complex", "file_path": "cache/complex_prompts/12a9940f3c2942ed9f0796bda4ab8697_complex_prompt.txt", "cached_at": "2025-07-25T00:50:19.778317", "prompt_length": 958, "metadata": {"lesson_name": "奇妙的撕纸", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:19.778140"}}, "9890f99d7855243f": {"lesson_id": "3949b468540e4df586da9430c6555316", "template_type": "complex", "file_path": "cache/complex_prompts/3949b468540e4df586da9430c6555316_complex_prompt.txt", "cached_at": "2025-07-25T00:50:21.815591", "prompt_length": 919, "metadata": {"lesson_name": "《设计剪纸》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:21.815437"}}, "97de47841a107a10": {"lesson_id": "9e033e7b6d9047f295fdd7cf3030df16", "template_type": "complex", "file_path": "cache/complex_prompts/9e033e7b6d9047f295fdd7cf3030df16_complex_prompt.txt", "cached_at": "2025-07-25T00:50:23.879186", "prompt_length": 927, "metadata": {"lesson_name": "用DIS测电源的电动势和内阻", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:23.879017"}}, "d084e18309483cac": {"lesson_id": "ebe33e3dd6de460f8bb118acf75d6c48", "template_type": "complex", "file_path": "cache/complex_prompts/ebe33e3dd6de460f8bb118acf75d6c48_complex_prompt.txt", "cached_at": "2025-07-25T00:50:25.953156", "prompt_length": 896, "metadata": {"lesson_name": "《数的趣味练习课》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:25.952996"}}, "2b12be2517540efc": {"lesson_id": "01ed8142da8445758339dfd030c49ea6", "template_type": "complex", "file_path": "cache/complex_prompts/01ed8142da8445758339dfd030c49ea6_complex_prompt.txt", "cached_at": "2025-07-25T00:50:28.069548", "prompt_length": 939, "metadata": {"lesson_name": "琥珀", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:28.069376"}}, "8c09f6750d1b241c": {"lesson_id": "eb6a2f335d8b456584e32101fa2f4b3c", "template_type": "complex", "file_path": "cache/complex_prompts/eb6a2f335d8b456584e32101fa2f4b3c_complex_prompt.txt", "cached_at": "2025-07-25T00:50:30.464302", "prompt_length": 952, "metadata": {"lesson_name": "手拉手模型（平台-全等三角形）", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:30.464109"}}, "67a3ff8ce11d0b29": {"lesson_id": "bf59433573b24effbee38c7fdea0608b", "template_type": "complex", "file_path": "cache/complex_prompts/bf59433573b24effbee38c7fdea0608b_complex_prompt.txt", "cached_at": "2025-07-25T00:50:32.534030", "prompt_length": 905, "metadata": {"lesson_name": "I'm short and fat", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:32.533849"}}, "dd64228668ccbe96": {"lesson_id": "2dbefbe0085b4a1a831797943fec750f", "template_type": "complex", "file_path": "cache/complex_prompts/2dbefbe0085b4a1a831797943fec750f_complex_prompt.txt", "cached_at": "2025-07-25T00:50:34.731481", "prompt_length": 878, "metadata": {"lesson_name": "角的初步认识", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:34.731299"}}, "54e176a5ffdae962": {"lesson_id": "52c6e784c75c4577ba760fa7a5ac9761", "template_type": "complex", "file_path": "cache/complex_prompts/52c6e784c75c4577ba760fa7a5ac9761_complex_prompt.txt", "cached_at": "2025-07-25T00:50:36.787902", "prompt_length": 949, "metadata": {"lesson_name": "Nanjing Road", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:36.787735"}}, "2cbf92708eb2b55b": {"lesson_id": "feb44c3aebdd4d84b904d8364d07954e", "template_type": "complex", "file_path": "cache/complex_prompts/feb44c3aebdd4d84b904d8364d07954e_complex_prompt.txt", "cached_at": "2025-07-25T00:50:38.873771", "prompt_length": 948, "metadata": {"lesson_name": "《课题1 分子和原子》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:38.873588"}}, "41f8f93add46f822": {"lesson_id": "2dc17cb0f12e4999ab99eb00f914dbbd", "template_type": "complex", "file_path": "cache/complex_prompts/2dc17cb0f12e4999ab99eb00f914dbbd_complex_prompt.txt", "cached_at": "2025-07-25T00:50:40.955815", "prompt_length": 898, "metadata": {"lesson_name": "《唐朝的中外文化交流》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:40.955655"}}, "fede0309ab368ef4": {"lesson_id": "d1ed9275a65340af89872d5497f08f4a", "template_type": "complex", "file_path": "cache/complex_prompts/d1ed9275a65340af89872d5497f08f4a_complex_prompt.txt", "cached_at": "2025-07-25T00:50:43.062371", "prompt_length": 949, "metadata": {"lesson_name": "4.1 线段、射线、直线", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:43.062199"}}, "c51e1bd3f9ff658d": {"lesson_id": "ab7b02c6d8a44c9fafc0b44ee7900018", "template_type": "complex", "file_path": "cache/complex_prompts/ab7b02c6d8a44c9fafc0b44ee7900018_complex_prompt.txt", "cached_at": "2025-07-25T00:50:45.146107", "prompt_length": 943, "metadata": {"lesson_name": "《坐井观天》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:45.145933"}}, "18af3c297fdb0a5e": {"lesson_id": "08c7684d890440ff95096e4b345453a6", "template_type": "complex", "file_path": "cache/complex_prompts/08c7684d890440ff95096e4b345453a6_complex_prompt.txt", "cached_at": "2025-07-25T00:50:47.648443", "prompt_length": 905, "metadata": {"lesson_name": "长方形和正方形", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:47.648286"}}, "5105b6d1383dd46c": {"lesson_id": "94ceef8004ef400a9d2c5c4e7522dfaa", "template_type": "complex", "file_path": "cache/complex_prompts/94ceef8004ef400a9d2c5c4e7522dfaa_complex_prompt.txt", "cached_at": "2025-07-25T00:50:49.729437", "prompt_length": 901, "metadata": {"lesson_name": "牧场之国", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:49.729277"}}, "c375a096b6603a91": {"lesson_id": "801f9539caa347c0abeb79b0b76c75cc", "template_type": "complex", "file_path": "cache/complex_prompts/801f9539caa347c0abeb79b0b76c75cc_complex_prompt.txt", "cached_at": "2025-07-25T00:50:51.820341", "prompt_length": 1062, "metadata": {"lesson_name": "Unit1 Welcome back to school", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:51.820161"}}, "ea479bb1afe8dcc5": {"lesson_id": "2739254eb84d413d90857c5cb87546e4", "template_type": "complex", "file_path": "cache/complex_prompts/2739254eb84d413d90857c5cb87546e4_complex_prompt.txt", "cached_at": "2025-07-25T00:50:53.914122", "prompt_length": 837, "metadata": {"lesson_name": "《垃圾分类》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:53.913945"}}, "6bd797c35f047c22": {"lesson_id": "6b6988ace7594d1ebb6bdf45e6e58c8e", "template_type": "complex", "file_path": "cache/complex_prompts/6b6988ace7594d1ebb6bdf45e6e58c8e_complex_prompt.txt", "cached_at": "2025-07-25T00:50:56.009852", "prompt_length": 849, "metadata": {"lesson_name": "三角形—三角形的有关概念及三边关系", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:56.009686"}}, "128f1de60cef5ecf": {"lesson_id": "a2e81afe43c241cea693b01518febd62", "template_type": "complex", "file_path": "cache/complex_prompts/a2e81afe43c241cea693b01518febd62_complex_prompt.txt", "cached_at": "2025-07-25T00:50:58.141774", "prompt_length": 879, "metadata": {"lesson_name": "《小猴子下山》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:50:58.141606"}}, "186c4d60cd08e7d9": {"lesson_id": "5896d85331324110b3ba4c7b601d3a63", "template_type": "complex", "file_path": "cache/complex_prompts/5896d85331324110b3ba4c7b601d3a63_complex_prompt.txt", "cached_at": "2025-07-25T00:51:00.242963", "prompt_length": 966, "metadata": {"lesson_name": "平面图形的面积-复习课", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:00.242775"}}, "f045a830d85c6ba3": {"lesson_id": "55537d7f4ff74a3fbdb3b3f77c16bab0", "template_type": "complex", "file_path": "cache/complex_prompts/55537d7f4ff74a3fbdb3b3f77c16bab0_complex_prompt.txt", "cached_at": "2025-07-25T00:51:02.442354", "prompt_length": 913, "metadata": {"lesson_name": "There are forty", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:02.442187"}}, "22ee24fa5ca5c65c": {"lesson_id": "beca283f3a3244fa9e1d4f2615e5d2f8", "template_type": "complex", "file_path": "cache/complex_prompts/beca283f3a3244fa9e1d4f2615e5d2f8_complex_prompt.txt", "cached_at": "2025-07-25T00:51:04.564380", "prompt_length": 837, "metadata": {"lesson_name": "猴王出世", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:04.564210"}}, "c66d158ae51bb408": {"lesson_id": "345b1019fffe465b8e7525c0a56f8acc", "template_type": "complex", "file_path": "cache/complex_prompts/345b1019fffe465b8e7525c0a56f8acc_complex_prompt.txt", "cached_at": "2025-07-25T00:51:06.698543", "prompt_length": 907, "metadata": {"lesson_name": "始终坚持以人民为中心", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:06.698357"}}, "dbf445db6938682d": {"lesson_id": "64e985b8e9f44d84876bd6084148ce43", "template_type": "complex", "file_path": "cache/complex_prompts/64e985b8e9f44d84876bd6084148ce43_complex_prompt.txt", "cached_at": "2025-07-25T00:51:08.807914", "prompt_length": 947, "metadata": {"lesson_name": "《分类与整理（一）》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:08.807746"}}, "d671256caa809434": {"lesson_id": "34957f85ee7542c8b82a1ba81cd754ec", "template_type": "complex", "file_path": "cache/complex_prompts/34957f85ee7542c8b82a1ba81cd754ec_complex_prompt.txt", "cached_at": "2025-07-25T00:51:11.034481", "prompt_length": 946, "metadata": {"lesson_name": "《观察物体（二）》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:11.034281"}}, "c95e8babe8668203": {"lesson_id": "47ce7e4b51394380b801c238c7508b29", "template_type": "complex", "file_path": "cache/complex_prompts/47ce7e4b51394380b801c238c7508b29_complex_prompt.txt", "cached_at": "2025-07-25T00:51:13.285572", "prompt_length": 904, "metadata": {"lesson_name": "《24 司马光》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:13.285406"}}, "92041d0931774e5d": {"lesson_id": "f6b2e6b5a1c3403cb76f1b57ec7a8c83", "template_type": "complex", "file_path": "cache/complex_prompts/f6b2e6b5a1c3403cb76f1b57ec7a8c83_complex_prompt.txt", "cached_at": "2025-07-25T00:51:15.386121", "prompt_length": 979, "metadata": {"lesson_name": "《制作“元气打call机”——初探人脸识别技术》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:15.385942"}}, "df0e3f62fad575ac": {"lesson_id": "82f078848b5840a49938cd0344d8ae68", "template_type": "complex", "file_path": "cache/complex_prompts/82f078848b5840a49938cd0344d8ae68_complex_prompt.txt", "cached_at": "2025-07-25T00:51:17.565952", "prompt_length": 897, "metadata": {"lesson_name": "走进Python编程", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:17.565731"}}, "bc22851b8498db46": {"lesson_id": "7df7c07c7e88450ca238e00bb0dbd87b", "template_type": "complex", "file_path": "cache/complex_prompts/7df7c07c7e88450ca238e00bb0dbd87b_complex_prompt.txt", "cached_at": "2025-07-25T00:51:19.774072", "prompt_length": 954, "metadata": {"lesson_name": "《第一章 网络的组建与运行》复习课", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:19.773885"}}, "91b45555922f48e2": {"lesson_id": "ac36ab6fdb84433ab9f1c66f86f07870", "template_type": "complex", "file_path": "cache/complex_prompts/ac36ab6fdb84433ab9f1c66f86f07870_complex_prompt.txt", "cached_at": "2025-07-25T00:51:21.889279", "prompt_length": 878, "metadata": {"lesson_name": "《观察物体》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:21.889104"}}, "eec8dc77eac83564": {"lesson_id": "c9c91e7828ab4ddcb858d93e70d5bf66", "template_type": "complex", "file_path": "cache/complex_prompts/c9c91e7828ab4ddcb858d93e70d5bf66_complex_prompt.txt", "cached_at": "2025-07-25T00:51:24.144384", "prompt_length": 905, "metadata": {"lesson_name": "《13.花钟》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:24.144205"}}, "095e73dbafa8ef5d": {"lesson_id": "bece75df221b4db5b5761d3d386f3bbe", "template_type": "complex", "file_path": "cache/complex_prompts/bece75df221b4db5b5761d3d386f3bbe_complex_prompt.txt", "cached_at": "2025-07-25T00:51:26.291084", "prompt_length": 948, "metadata": {"lesson_name": "第三课《民间装饰色彩》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:26.290909"}}, "8b8a385ae24542fc": {"lesson_id": "768bfd5762dd4e09bae2866d34f3b98d", "template_type": "complex", "file_path": "cache/complex_prompts/768bfd5762dd4e09bae2866d34f3b98d_complex_prompt.txt", "cached_at": "2025-07-25T00:51:28.356926", "prompt_length": 949, "metadata": {"lesson_name": "传统节日", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:28.356741"}}, "a3ed11321461f32a": {"lesson_id": "bd3e1e93eff2401e92540ac8709b1310", "template_type": "complex", "file_path": "cache/complex_prompts/bd3e1e93eff2401e92540ac8709b1310_complex_prompt.txt", "cached_at": "2025-07-25T00:51:30.711474", "prompt_length": 877, "metadata": {"lesson_name": "整式的加减", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:30.711307"}}, "a07fcf4aa096db31": {"lesson_id": "889e5728b4c648a7890b0cdf80ce0aa2", "template_type": "complex", "file_path": "cache/complex_prompts/889e5728b4c648a7890b0cdf80ce0aa2_complex_prompt.txt", "cached_at": "2025-07-25T00:51:32.866199", "prompt_length": 913, "metadata": {"lesson_name": "Unit2 Friends and Colours", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:32.866033"}}, "8ab61e438804c3ef": {"lesson_id": "42e5f3fe5af64d968bbd1a2eb08a2a52", "template_type": "complex", "file_path": "cache/complex_prompts/42e5f3fe5af64d968bbd1a2eb08a2a52_complex_prompt.txt", "cached_at": "2025-07-25T00:51:34.940208", "prompt_length": 985, "metadata": {"lesson_name": "微笑的程小奔", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:34.940029"}}, "972320360490c1e7": {"lesson_id": "fc7305c146ac488e96e807f95f6f710c", "template_type": "complex", "file_path": "cache/complex_prompts/fc7305c146ac488e96e807f95f6f710c_complex_prompt.txt", "cached_at": "2025-07-25T00:51:37.014600", "prompt_length": 906, "metadata": {"lesson_name": "《大自然的声音》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:37.014430"}}, "79d7827381eb0374": {"lesson_id": "e2f2f3e1fcde439cbb688a399462fbfb", "template_type": "complex", "file_path": "cache/complex_prompts/e2f2f3e1fcde439cbb688a399462fbfb_complex_prompt.txt", "cached_at": "2025-07-25T00:51:39.103036", "prompt_length": 948, "metadata": {"lesson_name": "When is Teacher's Day", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:39.102838"}}, "fdba2466695fe4e1": {"lesson_id": "8e5a185ed6aa4e748f7a75b1af10e257", "template_type": "complex", "file_path": "cache/complex_prompts/8e5a185ed6aa4e748f7a75b1af10e257_complex_prompt.txt", "cached_at": "2025-07-25T00:51:41.139051", "prompt_length": 892, "metadata": {"lesson_name": "两小儿辩日", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:41.138877"}}, "c6dd7ad04df0168a": {"lesson_id": "35aaa81e1cf54c7e95ad907756fed6e0", "template_type": "complex", "file_path": "cache/complex_prompts/35aaa81e1cf54c7e95ad907756fed6e0_complex_prompt.txt", "cached_at": "2025-07-25T00:51:43.173181", "prompt_length": 938, "metadata": {"lesson_name": "《食物在身体里的旅行》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:43.173016"}}, "a65d6c40b37d12cf": {"lesson_id": "46f9409fda984bb18cbae494a8878a3c", "template_type": "complex", "file_path": "cache/complex_prompts/46f9409fda984bb18cbae494a8878a3c_complex_prompt.txt", "cached_at": "2025-07-25T00:51:45.432507", "prompt_length": 932, "metadata": {"lesson_name": "一棵小桃树", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:45.432326"}}, "e63d6cfc10bacbbd": {"lesson_id": "17970006863e4e4da5bc5c53145f960c", "template_type": "complex", "file_path": "cache/complex_prompts/17970006863e4e4da5bc5c53145f960c_complex_prompt.txt", "cached_at": "2025-07-25T00:51:47.593139", "prompt_length": 906, "metadata": {"lesson_name": "1.用字母表示数", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:47.592962"}}, "7a19af0514990e7c": {"lesson_id": "0b882f2e472a491cace3ae7fb8a1af01", "template_type": "complex", "file_path": "cache/complex_prompts/0b882f2e472a491cace3ae7fb8a1af01_complex_prompt.txt", "cached_at": "2025-07-25T00:51:49.940661", "prompt_length": 865, "metadata": {"lesson_name": "王戎不取道旁李", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:49.940479"}}, "a928c6117e4ffe82": {"lesson_id": "4f5be2936e7347cba74f23d539a27ef4", "template_type": "complex", "file_path": "cache/complex_prompts/4f5be2936e7347cba74f23d539a27ef4_complex_prompt.txt", "cached_at": "2025-07-25T00:51:52.023199", "prompt_length": 878, "metadata": {"lesson_name": "《5.1.3 同位角、内错角、同旁内角》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:52.023019"}}, "65df0df34ef758ef": {"lesson_id": "68d0b0d2ac864af381ba18c1468a12ab", "template_type": "complex", "file_path": "cache/complex_prompts/68d0b0d2ac864af381ba18c1468a12ab_complex_prompt.txt", "cached_at": "2025-07-25T00:51:54.065335", "prompt_length": 1005, "metadata": {"lesson_name": "《函数中的零点问题》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:54.065155"}}, "e7cefe4aa8ad2640": {"lesson_id": "24cecfa2449d4281880cdee26d680a26", "template_type": "complex", "file_path": "cache/complex_prompts/24cecfa2449d4281880cdee26d680a26_complex_prompt.txt", "cached_at": "2025-07-25T00:51:56.161877", "prompt_length": 881, "metadata": {"lesson_name": "《认识我们的朋友》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:56.161704"}}, "4daad1dd0fa32057": {"lesson_id": "6939e6fdb749437aa3848572f9c3b1fa", "template_type": "complex", "file_path": "cache/complex_prompts/6939e6fdb749437aa3848572f9c3b1fa_complex_prompt.txt", "cached_at": "2025-07-25T00:51:58.275464", "prompt_length": 985, "metadata": {"lesson_name": "《西北地区》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:51:58.275296"}}, "0451153baed4fb3b": {"lesson_id": "40c5a72f74224cdba1cff4f4d9843302", "template_type": "complex", "file_path": "cache/complex_prompts/40c5a72f74224cdba1cff4f4d9843302_complex_prompt.txt", "cached_at": "2025-07-25T00:52:00.360888", "prompt_length": 914, "metadata": {"lesson_name": "Beautiful flowers", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:00.360718"}}, "75bf0330cdd24936": {"lesson_id": "390c03c3b8254175b4634ba07c1148d8", "template_type": "complex", "file_path": "cache/complex_prompts/390c03c3b8254175b4634ba07c1148d8_complex_prompt.txt", "cached_at": "2025-07-25T00:52:02.627000", "prompt_length": 945, "metadata": {"lesson_name": "《24 司马光》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:02.626817"}}, "b47a407490091be2": {"lesson_id": "47068c8986f74bbfb5ec8949c414b069", "template_type": "complex", "file_path": "cache/complex_prompts/47068c8986f74bbfb5ec8949c414b069_complex_prompt.txt", "cached_at": "2025-07-25T00:52:04.848040", "prompt_length": 1014, "metadata": {"lesson_name": "Module6 Occupations", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:04.847858"}}, "cf4848614bfb26a7": {"lesson_id": "d608861264dd4828983cf22fd03969ba", "template_type": "complex", "file_path": "cache/complex_prompts/d608861264dd4828983cf22fd03969ba_complex_prompt.txt", "cached_at": "2025-07-25T00:52:06.962864", "prompt_length": 941, "metadata": {"lesson_name": "有机合成", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:06.962713"}}, "b464879bc449bed4": {"lesson_id": "93eef61642a94e35b1e1c92962aa5630", "template_type": "complex", "file_path": "cache/complex_prompts/93eef61642a94e35b1e1c92962aa5630_complex_prompt.txt", "cached_at": "2025-07-25T00:52:09.045774", "prompt_length": 1001, "metadata": {"lesson_name": "Unit8 Chinese New Year", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:09.045606"}}, "0eff449a879f6b74": {"lesson_id": "029f4fee1d244d5dbc85fe090ba09ea7", "template_type": "complex", "file_path": "cache/complex_prompts/029f4fee1d244d5dbc85fe090ba09ea7_complex_prompt.txt", "cached_at": "2025-07-25T00:52:11.105368", "prompt_length": 920, "metadata": {"lesson_name": "心脏和血液循环", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:11.105191"}}, "8c9fe0046f3c0549": {"lesson_id": "ebfac01cc8404803a8fa047319203fc2", "template_type": "complex", "file_path": "cache/complex_prompts/ebfac01cc8404803a8fa047319203fc2_complex_prompt.txt", "cached_at": "2025-07-25T00:52:13.168574", "prompt_length": 907, "metadata": {"lesson_name": "《这样想象真有趣》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:13.168404"}}, "5826a3d34799eb06": {"lesson_id": "e89e54bf27d9456a8d534e00726b616a", "template_type": "complex", "file_path": "cache/complex_prompts/e89e54bf27d9456a8d534e00726b616a_complex_prompt.txt", "cached_at": "2025-07-25T00:52:15.269105", "prompt_length": 950, "metadata": {"lesson_name": "《23 *梅兰芳蓄须》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:15.268933"}}, "6a815f5545bf6e33": {"lesson_id": "57358e136cee4094bc9189f67524307c", "template_type": "complex", "file_path": "cache/complex_prompts/57358e136cee4094bc9189f67524307c_complex_prompt.txt", "cached_at": "2025-07-25T00:52:17.308347", "prompt_length": 907, "metadata": {"lesson_name": "叶子上的“小血管”", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:17.308189"}}, "3e390dfeeed99f15": {"lesson_id": "1c4e847b72834cbe89e562d924e86874", "template_type": "complex", "file_path": "cache/complex_prompts/1c4e847b72834cbe89e562d924e86874_complex_prompt.txt", "cached_at": "2025-07-25T00:52:19.420275", "prompt_length": 1003, "metadata": {"lesson_name": "班级建设 人人有责", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:19.420097"}}, "8558d8d887f3f1b6": {"lesson_id": "65c0901de609491a8c94025bcf09b5a0", "template_type": "complex", "file_path": "cache/complex_prompts/65c0901de609491a8c94025bcf09b5a0_complex_prompt.txt", "cached_at": "2025-07-25T00:52:21.438153", "prompt_length": 962, "metadata": {"lesson_name": "金属与酸的反应再探究", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:21.437976"}}, "31397784d9c9abe6": {"lesson_id": "122c0c5d9c97409aaca465b592738747", "template_type": "complex", "file_path": "cache/complex_prompts/122c0c5d9c97409aaca465b592738747_complex_prompt.txt", "cached_at": "2025-07-25T00:52:23.403500", "prompt_length": 896, "metadata": {"lesson_name": "《正视发展挑战》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:23.403324"}}, "c91e14fbef577b50": {"lesson_id": "7989413e825641b1b0ea469259838774", "template_type": "complex", "file_path": "cache/complex_prompts/7989413e825641b1b0ea469259838774_complex_prompt.txt", "cached_at": "2025-07-25T00:52:25.525785", "prompt_length": 995, "metadata": {"lesson_name": "向心力", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:25.525617"}}, "4be05fd2b1ba760a": {"lesson_id": "614bf13a49564e79a32c866d5377caa6", "template_type": "complex", "file_path": "cache/complex_prompts/614bf13a49564e79a32c866d5377caa6_complex_prompt.txt", "cached_at": "2025-07-25T00:52:27.718167", "prompt_length": 880, "metadata": {"lesson_name": "Unit6 In a nature park", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:27.717993"}}, "f657ed8038b62d60": {"lesson_id": "91e9a70d41c74957bd9e353fd764669d", "template_type": "complex", "file_path": "cache/complex_prompts/91e9a70d41c74957bd9e353fd764669d_complex_prompt.txt", "cached_at": "2025-07-25T00:52:29.807690", "prompt_length": 1000, "metadata": {"lesson_name": "植物体的组成", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:29.807524"}}, "bab04cb0478ee84b": {"lesson_id": "d2407b4d4128489bbbb79817183aec1c", "template_type": "complex", "file_path": "cache/complex_prompts/d2407b4d4128489bbbb79817183aec1c_complex_prompt.txt", "cached_at": "2025-07-25T00:52:31.852272", "prompt_length": 902, "metadata": {"lesson_name": "美丽的纹样", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:31.852091"}}, "a4e99818e27819fa": {"lesson_id": "6a8691d4cebe4413aa22043aa2c72f82", "template_type": "complex", "file_path": "cache/complex_prompts/6a8691d4cebe4413aa22043aa2c72f82_complex_prompt.txt", "cached_at": "2025-07-25T00:52:33.942124", "prompt_length": 901, "metadata": {"lesson_name": "狐狸分奶酪", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:33.941952"}}, "111e8db7360fdf87": {"lesson_id": "bb329411cddd48f7b26a084915108e5d", "template_type": "complex", "file_path": "cache/complex_prompts/bb329411cddd48f7b26a084915108e5d_complex_prompt.txt", "cached_at": "2025-07-25T00:52:36.215314", "prompt_length": 958, "metadata": {"lesson_name": "《数豆子》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:36.215134"}}, "d19856c2ea38f7c1": {"lesson_id": "cc4d453c33b8481c8ba500598aab1d0f", "template_type": "complex", "file_path": "cache/complex_prompts/cc4d453c33b8481c8ba500598aab1d0f_complex_prompt.txt", "cached_at": "2025-07-25T00:52:38.316906", "prompt_length": 909, "metadata": {"lesson_name": "《基本指法和常用按键》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:38.316746"}}, "36917d49e9f6b20f": {"lesson_id": "9dce4782bd57402aacb2ab0ab3d9154c", "template_type": "complex", "file_path": "cache/complex_prompts/9dce4782bd57402aacb2ab0ab3d9154c_complex_prompt.txt", "cached_at": "2025-07-25T00:52:40.481043", "prompt_length": 996, "metadata": {"lesson_name": "车的运动", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:40.480864"}}, "ea9292f26fb30021": {"lesson_id": "81f57372457344a0b57f80ab02d4d680", "template_type": "complex", "file_path": "cache/complex_prompts/81f57372457344a0b57f80ab02d4d680_complex_prompt.txt", "cached_at": "2025-07-25T00:52:42.840606", "prompt_length": 908, "metadata": {"lesson_name": "二次函数的图像和性质", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:42.840431"}}, "5d3a77230814fdea": {"lesson_id": "3bbdf792ceed47adbaa2507ab83aa1fa", "template_type": "complex", "file_path": "cache/complex_prompts/3bbdf792ceed47adbaa2507ab83aa1fa_complex_prompt.txt", "cached_at": "2025-07-25T00:52:44.890904", "prompt_length": 962, "metadata": {"lesson_name": "《声音的特性》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:44.890738"}}, "f966dc6d1d08d021": {"lesson_id": "5b530338ccb44289a23f18e64e6c10b0", "template_type": "complex", "file_path": "cache/complex_prompts/5b530338ccb44289a23f18e64e6c10b0_complex_prompt.txt", "cached_at": "2025-07-25T00:52:46.975822", "prompt_length": 839, "metadata": {"lesson_name": "《合理消费》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:46.975643"}}, "8354a8285bb77fad": {"lesson_id": "8b1e021601a1426f83d13625403ff67c", "template_type": "complex", "file_path": "cache/complex_prompts/8b1e021601a1426f83d13625403ff67c_complex_prompt.txt", "cached_at": "2025-07-25T00:52:49.056353", "prompt_length": 974, "metadata": {"lesson_name": "一幅名扬中国的画（应为：一幅名扬中外的画）", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:49.056191"}}, "90ebbb17d1e126dc": {"lesson_id": "673d315487d94083ac26e62ed23293f6", "template_type": "complex", "file_path": "cache/complex_prompts/673d315487d94083ac26e62ed23293f6_complex_prompt.txt", "cached_at": "2025-07-25T00:52:51.104191", "prompt_length": 861, "metadata": {"lesson_name": "中国美食", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:51.104020"}}, "cd793456d695992d": {"lesson_id": "f847426d11e84a1e853f01214af42941", "template_type": "complex", "file_path": "cache/complex_prompts/f847426d11e84a1e853f01214af42941_complex_prompt.txt", "cached_at": "2025-07-25T00:52:53.239863", "prompt_length": 844, "metadata": {"lesson_name": "《数学广角——植树问题》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:53.239685"}}, "b6ff4fcf9e9c6b57": {"lesson_id": "39fc1b0cecbb4076a31d3f5cb24181f9", "template_type": "complex", "file_path": "cache/complex_prompts/39fc1b0cecbb4076a31d3f5cb24181f9_complex_prompt.txt", "cached_at": "2025-07-25T00:52:55.302851", "prompt_length": 977, "metadata": {"lesson_name": "Unit3 Allabout me Lesson17 What do you like to do?", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:55.302678"}}, "c3c9e1bda55d6dab": {"lesson_id": "dfa5d26577654a3baa1eee5891245cff", "template_type": "complex", "file_path": "cache/complex_prompts/dfa5d26577654a3baa1eee5891245cff_complex_prompt.txt", "cached_at": "2025-07-25T00:52:57.366475", "prompt_length": 903, "metadata": {"lesson_name": "《摩擦力》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:57.365632"}}, "9b976d6a6f096f9a": {"lesson_id": "684c37bd713344c196300f9b9b582697", "template_type": "complex", "file_path": "cache/complex_prompts/684c37bd713344c196300f9b9b582697_complex_prompt.txt", "cached_at": "2025-07-25T00:52:59.425501", "prompt_length": 910, "metadata": {"lesson_name": "Welcome back to school", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:52:59.425327"}}, "49065cfd3d15aff5": {"lesson_id": "1e40e0663abc4262bc5b08d8e8135c92", "template_type": "complex", "file_path": "cache/complex_prompts/1e40e0663abc4262bc5b08d8e8135c92_complex_prompt.txt", "cached_at": "2025-07-25T00:53:01.514583", "prompt_length": 993, "metadata": {"lesson_name": "绿", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:01.514407"}}, "b041ce7b8844e077": {"lesson_id": "000818da4e444e2a97ce3cebcb1be305", "template_type": "complex", "file_path": "cache/complex_prompts/000818da4e444e2a97ce3cebcb1be305_complex_prompt.txt", "cached_at": "2025-07-25T00:53:03.606311", "prompt_length": 944, "metadata": {"lesson_name": "《汉字真有趣》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:03.606143"}}, "74166044dc4cfa6e": {"lesson_id": "a3e9c4bfd673453d917f73c0d83597a4", "template_type": "complex", "file_path": "cache/complex_prompts/a3e9c4bfd673453d917f73c0d83597a4_complex_prompt.txt", "cached_at": "2025-07-25T00:53:05.719456", "prompt_length": 866, "metadata": {"lesson_name": "钉子板上的多边形", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:05.719289"}}, "b8d3132ab32e63bd": {"lesson_id": "e2b4522d12794cb399ecb3bd58deb9d9", "template_type": "complex", "file_path": "cache/complex_prompts/e2b4522d12794cb399ecb3bd58deb9d9_complex_prompt.txt", "cached_at": "2025-07-25T00:53:07.781098", "prompt_length": 939, "metadata": {"lesson_name": "鸟类", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:07.780913"}}, "99cb49c77432ce84": {"lesson_id": "454c6855dd374f31b31da2bfafadd62c", "template_type": "complex", "file_path": "cache/complex_prompts/454c6855dd374f31b31da2bfafadd62c_complex_prompt.txt", "cached_at": "2025-07-25T00:53:09.951905", "prompt_length": 874, "metadata": {"lesson_name": "跳水", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:09.951733"}}, "0fc1b48682b7319d": {"lesson_id": "584aa20eea2b4ef7802f7896fa4ddcf2", "template_type": "complex", "file_path": "cache/complex_prompts/584aa20eea2b4ef7802f7896fa4ddcf2_complex_prompt.txt", "cached_at": "2025-07-25T00:53:12.194273", "prompt_length": 935, "metadata": {"lesson_name": "3 角的初步认识", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:12.194114"}}, "01d60e5e4c6d1702": {"lesson_id": "ee6220695d0748358e4e4f2ed12c5a8e", "template_type": "complex", "file_path": "cache/complex_prompts/ee6220695d0748358e4e4f2ed12c5a8e_complex_prompt.txt", "cached_at": "2025-07-25T00:53:14.262506", "prompt_length": 931, "metadata": {"lesson_name": "第一单元", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:14.262334"}}, "76acc17d8c70f4eb": {"lesson_id": "8342863cb0694029b8624a600f5cc9f0", "template_type": "complex", "file_path": "cache/complex_prompts/8342863cb0694029b8624a600f5cc9f0_complex_prompt.txt", "cached_at": "2025-07-25T00:53:16.338621", "prompt_length": 978, "metadata": {"lesson_name": "《立刀旁与力字旁》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:16.338451"}}, "a44665dbb5c6f692": {"lesson_id": "19229a930aad4ed0b1466e467d481a59", "template_type": "complex", "file_path": "cache/complex_prompts/19229a930aad4ed0b1466e467d481a59_complex_prompt.txt", "cached_at": "2025-07-25T00:53:18.422352", "prompt_length": 850, "metadata": {"lesson_name": "花灯", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:18.422181"}}, "612f4ef03f7f5e93": {"lesson_id": "038855d279d44986893c36535a124410", "template_type": "complex", "file_path": "cache/complex_prompts/038855d279d44986893c36535a124410_complex_prompt.txt", "cached_at": "2025-07-25T00:53:20.518140", "prompt_length": 917, "metadata": {"lesson_name": "认识水果", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:20.517964"}}, "d48222db30bedf4f": {"lesson_id": "ed11e40267914b7fb0486177d50295f9", "template_type": "complex", "file_path": "cache/complex_prompts/ed11e40267914b7fb0486177d50295f9_complex_prompt.txt", "cached_at": "2025-07-25T00:53:22.626262", "prompt_length": 858, "metadata": {"lesson_name": "长方体和正方体的认识", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:22.626073"}}, "3045d218295f6822": {"lesson_id": "8982016bd88c4d6bb0108df10566a5d2", "template_type": "complex", "file_path": "cache/complex_prompts/8982016bd88c4d6bb0108df10566a5d2_complex_prompt.txt", "cached_at": "2025-07-25T00:53:24.732165", "prompt_length": 909, "metadata": {"lesson_name": "综合性学习-古诗苑漫步", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:24.731994"}}, "90e7c911d5879945": {"lesson_id": "d88308bb992f43efb964aceb0a6f192e", "template_type": "complex", "file_path": "cache/complex_prompts/d88308bb992f43efb964aceb0a6f192e_complex_prompt.txt", "cached_at": "2025-07-25T00:53:26.972155", "prompt_length": 948, "metadata": {"lesson_name": "《构建基因的表达载体》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:26.971981"}}, "cc76a11d69acca2a": {"lesson_id": "aeb769eda457429fbedb85933264647a", "template_type": "complex", "file_path": "cache/complex_prompts/aeb769eda457429fbedb85933264647a_complex_prompt.txt", "cached_at": "2025-07-25T00:53:29.031426", "prompt_length": 884, "metadata": {"lesson_name": "第2节　基因工程及其应用", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:29.031260"}}, "491c87b9d4d44e2f": {"lesson_id": "cd271c45ac6546288874bb27f1c43256", "template_type": "complex", "file_path": "cache/complex_prompts/cd271c45ac6546288874bb27f1c43256_complex_prompt.txt", "cached_at": "2025-07-25T00:53:31.261970", "prompt_length": 902, "metadata": {"lesson_name": "小猴子下山", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:31.261796"}}, "093041a4ee7d0640": {"lesson_id": "6fe390de4cb544d7956061a1326f825b", "template_type": "complex", "file_path": "cache/complex_prompts/6fe390de4cb544d7956061a1326f825b_complex_prompt.txt", "cached_at": "2025-07-25T00:53:33.330267", "prompt_length": 906, "metadata": {"lesson_name": "《二元一次方程组》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:33.329176"}}, "5c38dbd8d5427df3": {"lesson_id": "924536acc7364043bb09263057897f6c", "template_type": "complex", "file_path": "cache/complex_prompts/924536acc7364043bb09263057897f6c_complex_prompt.txt", "cached_at": "2025-07-25T00:53:35.518205", "prompt_length": 998, "metadata": {"lesson_name": "立体交通", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:35.518031"}}, "8ef7e0e4b035465c": {"lesson_id": "63bb5c97d8a84a8281a3cf93615b678f", "template_type": "complex", "file_path": "cache/complex_prompts/63bb5c97d8a84a8281a3cf93615b678f_complex_prompt.txt", "cached_at": "2025-07-25T00:53:37.649216", "prompt_length": 860, "metadata": {"lesson_name": "《人工智能之神奇的算法》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:37.649045"}}, "fa80976be151cc20": {"lesson_id": "24b8e8bc63ed498eb2c828652538ff85", "template_type": "complex", "file_path": "cache/complex_prompts/24b8e8bc63ed498eb2c828652538ff85_complex_prompt.txt", "cached_at": "2025-07-25T00:53:39.901899", "prompt_length": 999, "metadata": {"lesson_name": "草船借箭", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:39.901725"}}, "535a4ea1f4c29617": {"lesson_id": "39d7605644d7412abdb62f448a1a6ed2", "template_type": "complex", "file_path": "cache/complex_prompts/39d7605644d7412abdb62f448a1a6ed2_complex_prompt.txt", "cached_at": "2025-07-25T00:53:41.952085", "prompt_length": 926, "metadata": {"lesson_name": "《Old london and london today》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:41.951905"}}, "c4ed7210a9fa1d1a": {"lesson_id": "9b1291b7c7b647e8a68941412cb04e08", "template_type": "complex", "file_path": "cache/complex_prompts/9b1291b7c7b647e8a68941412cb04e08_complex_prompt.txt", "cached_at": "2025-07-25T00:53:44.012671", "prompt_length": 1044, "metadata": {"lesson_name": "《茅屋为秋风所破歌》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:44.012515"}}, "cb7e6e66c2a6b8f0": {"lesson_id": "a829929d1fb349cea7c4c99ceb508ee9", "template_type": "complex", "file_path": "cache/complex_prompts/a829929d1fb349cea7c4c99ceb508ee9_complex_prompt.txt", "cached_at": "2025-07-25T00:53:46.089944", "prompt_length": 1042, "metadata": {"lesson_name": "我做了一项小实验", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:46.089769"}}, "91363dae38746467": {"lesson_id": "74198a564240461e91192e33f1451d84", "template_type": "complex", "file_path": "cache/complex_prompts/74198a564240461e91192e33f1451d84_complex_prompt.txt", "cached_at": "2025-07-25T00:53:48.256142", "prompt_length": 999, "metadata": {"lesson_name": "汉字真有趣", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:48.255971"}}, "fccf9cace2780a64": {"lesson_id": "60c3c12846aa47d7a0f4b383dacbbf1b", "template_type": "complex", "file_path": "cache/complex_prompts/60c3c12846aa47d7a0f4b383dacbbf1b_complex_prompt.txt", "cached_at": "2025-07-25T00:53:50.369625", "prompt_length": 893, "metadata": {"lesson_name": "Unit2 Lessons in life", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:50.369461"}}, "39b546ec52d32783": {"lesson_id": "0b9fe1e908744a27a25f0ba9d701c716", "template_type": "complex", "file_path": "cache/complex_prompts/0b9fe1e908744a27a25f0ba9d701c716_complex_prompt.txt", "cached_at": "2025-07-25T00:53:52.418788", "prompt_length": 903, "metadata": {"lesson_name": "函数的基本性质", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:52.418106"}}, "0bea34e766970847": {"lesson_id": "f8120412786b48a7bd4dac77d255f35e", "template_type": "complex", "file_path": "cache/complex_prompts/f8120412786b48a7bd4dac77d255f35e_complex_prompt.txt", "cached_at": "2025-07-25T00:53:54.896583", "prompt_length": 1020, "metadata": {"lesson_name": "Unit2 Colorful Fruit Friends", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:54.896403"}}, "f53a0b5eda00330f": {"lesson_id": "7115760f3ceb4df8877b891065847ec8", "template_type": "complex", "file_path": "cache/complex_prompts/7115760f3ceb4df8877b891065847ec8_complex_prompt.txt", "cached_at": "2025-07-25T00:53:57.119183", "prompt_length": 824, "metadata": {"lesson_name": "《DNA是主要的遗传物质》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:57.119006"}}, "d52dabe20c29223a": {"lesson_id": "a7ea4f7089c24357b529aca97b562aaf", "template_type": "complex", "file_path": "cache/complex_prompts/a7ea4f7089c24357b529aca97b562aaf_complex_prompt.txt", "cached_at": "2025-07-25T00:53:59.444379", "prompt_length": 877, "metadata": {"lesson_name": "九、可能性", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:53:59.444192"}}, "b5237647221bbce8": {"lesson_id": "694b0cdd033d4d148a120e2be8744faf", "template_type": "complex", "file_path": "cache/complex_prompts/694b0cdd033d4d148a120e2be8744faf_complex_prompt.txt", "cached_at": "2025-07-25T00:54:01.443572", "prompt_length": 919, "metadata": {"lesson_name": "《认识面积》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:01.443394"}}, "b40155812075f750": {"lesson_id": "f115592304e4449989edeb1e710b611d", "template_type": "complex", "file_path": "cache/complex_prompts/f115592304e4449989edeb1e710b611d_complex_prompt.txt", "cached_at": "2025-07-25T00:54:03.715732", "prompt_length": 944, "metadata": {"lesson_name": "分数的初步认识", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:03.715560"}}, "caf5db4471d5a15c": {"lesson_id": "bb25cfcefd674c538ffa6ae73416e0ce", "template_type": "complex", "file_path": "cache/complex_prompts/bb25cfcefd674c538ffa6ae73416e0ce_complex_prompt.txt", "cached_at": "2025-07-25T00:54:05.812123", "prompt_length": 838, "metadata": {"lesson_name": "氧化还原反应", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:05.811954"}}, "059eb835f6fa14dd": {"lesson_id": "3d4d7a7430ef4c0ba0a801d2637df002", "template_type": "complex", "file_path": "cache/complex_prompts/3d4d7a7430ef4c0ba0a801d2637df002_complex_prompt.txt", "cached_at": "2025-07-25T00:54:07.885229", "prompt_length": 980, "metadata": {"lesson_name": "unit4 Don't eat in class section B(2b-2c)", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:07.885050"}}, "aa32014341e41077": {"lesson_id": "d9dc3654362a45168a05ef316f225317", "template_type": "complex", "file_path": "cache/complex_prompts/d9dc3654362a45168a05ef316f225317_complex_prompt.txt", "cached_at": "2025-07-25T00:54:10.186079", "prompt_length": 957, "metadata": {"lesson_name": "明天要远足", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:10.185900"}}, "032bd324adfa6969": {"lesson_id": "f029bb749443413ebd8479b795820539", "template_type": "complex", "file_path": "cache/complex_prompts/f029bb749443413ebd8479b795820539_complex_prompt.txt", "cached_at": "2025-07-25T00:54:12.338249", "prompt_length": 940, "metadata": {"lesson_name": "竹里馆", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:12.338092"}}, "927d89020ddaada5": {"lesson_id": "cce74790a3c64328943b590f9b17702e", "template_type": "complex", "file_path": "cache/complex_prompts/cce74790a3c64328943b590f9b17702e_complex_prompt.txt", "cached_at": "2025-07-25T00:54:14.467241", "prompt_length": 865, "metadata": {"lesson_name": "《认识传感器》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:14.467070"}}, "ac7a66f945aa1e5a": {"lesson_id": "77ff86010a5f444cba9c3822a601040c", "template_type": "complex", "file_path": "cache/complex_prompts/77ff86010a5f444cba9c3822a601040c_complex_prompt.txt", "cached_at": "2025-07-25T00:54:16.706622", "prompt_length": 949, "metadata": {"lesson_name": "《汉语拼音 语文园地三》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:16.706459"}}, "a333ac1d3081efa7": {"lesson_id": "b1d395da74ec45448bd7e40bfb71a165", "template_type": "complex", "file_path": "cache/complex_prompts/b1d395da74ec45448bd7e40bfb71a165_complex_prompt.txt", "cached_at": "2025-07-25T00:54:18.752883", "prompt_length": 969, "metadata": {"lesson_name": "Unit6 I'm going to study computer science.", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:18.752708"}}, "4a52840781cfaec9": {"lesson_id": "4c2fa11f8c1f42a4815fd9cd70534f7a", "template_type": "complex", "file_path": "cache/complex_prompts/4c2fa11f8c1f42a4815fd9cd70534f7a_complex_prompt.txt", "cached_at": "2025-07-25T00:54:20.828001", "prompt_length": 944, "metadata": {"lesson_name": "《分类与整理》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:20.827831"}}, "ddb5b6b8d2fab835": {"lesson_id": "0c2cb9b0350045dd87a75e7e9f281385", "template_type": "complex", "file_path": "cache/complex_prompts/0c2cb9b0350045dd87a75e7e9f281385_complex_prompt.txt", "cached_at": "2025-07-25T00:54:22.960742", "prompt_length": 946, "metadata": {"lesson_name": "《青铜器与甲骨文》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:22.960563"}}, "64243547eeeff4a6": {"lesson_id": "33c09f21127f4b2ba3f415ef4a43a00e", "template_type": "complex", "file_path": "cache/complex_prompts/33c09f21127f4b2ba3f415ef4a43a00e_complex_prompt.txt", "cached_at": "2025-07-25T00:54:25.336438", "prompt_length": 959, "metadata": {"lesson_name": "7.2 坐标方法的简单的应用 第七章 7.2.2 用坐标表示平移", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:25.336276"}}, "deab753fbd09e695": {"lesson_id": "d7a896dce5b648a39502499870c07f67", "template_type": "complex", "file_path": "cache/complex_prompts/d7a896dce5b648a39502499870c07f67_complex_prompt.txt", "cached_at": "2025-07-25T00:54:27.434813", "prompt_length": 941, "metadata": {"lesson_name": "精卫填海", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:27.434646"}}, "24ea7e35d4945732": {"lesson_id": "d562c64e40a648539f0d170cda89e3f4", "template_type": "complex", "file_path": "cache/complex_prompts/d562c64e40a648539f0d170cda89e3f4_complex_prompt.txt", "cached_at": "2025-07-25T00:54:29.482359", "prompt_length": 890, "metadata": {"lesson_name": "《对读后续写中喜与怒情感描写的探索》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:29.482194"}}, "fa9267d70b08cd4e": {"lesson_id": "2240b4b03c0d42c99973186a9d4b976a", "template_type": "complex", "file_path": "cache/complex_prompts/2240b4b03c0d42c99973186a9d4b976a_complex_prompt.txt", "cached_at": "2025-07-25T00:54:31.521527", "prompt_length": 944, "metadata": {"lesson_name": "动起来，动起来", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:31.521364"}}, "8b3a1c95c1b2d7eb": {"lesson_id": "528b517c40854e0eb1b09c302a5a689d", "template_type": "complex", "file_path": "cache/complex_prompts/528b517c40854e0eb1b09c302a5a689d_complex_prompt.txt", "cached_at": "2025-07-25T00:54:33.565471", "prompt_length": 931, "metadata": {"lesson_name": "法不可违", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:33.565291"}}, "212b946336206a7c": {"lesson_id": "84a5aabf1e74457c89b09310fcab5ef6", "template_type": "complex", "file_path": "cache/complex_prompts/84a5aabf1e74457c89b09310fcab5ef6_complex_prompt.txt", "cached_at": "2025-07-25T00:54:35.749747", "prompt_length": 865, "metadata": {"lesson_name": "天然水的人工进化", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:35.749579"}}, "46cc546fdb6c5346": {"lesson_id": "1911c3d3862f413e9463730bb21e41e6", "template_type": "complex", "file_path": "cache/complex_prompts/1911c3d3862f413e9463730bb21e41e6_complex_prompt.txt", "cached_at": "2025-07-25T00:54:37.890817", "prompt_length": 944, "metadata": {"lesson_name": "Unit3 Revision 平台上 Unit3 How do you get school", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:37.890649"}}, "d49e70ee4d30f34c": {"lesson_id": "c8deba753079449f957997109d4de5f6", "template_type": "complex", "file_path": "cache/complex_prompts/c8deba753079449f957997109d4de5f6_complex_prompt.txt", "cached_at": "2025-07-25T00:54:40.191079", "prompt_length": 927, "metadata": {"lesson_name": "Thansk giving in North America", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:40.190911"}}, "777055075bf48b4a": {"lesson_id": "3369c0af3a8e428fbd453e5a4e7f78c2", "template_type": "complex", "file_path": "cache/complex_prompts/3369c0af3a8e428fbd453e5a4e7f78c2_complex_prompt.txt", "cached_at": "2025-07-25T00:54:42.234764", "prompt_length": 957, "metadata": {"lesson_name": "《东南亚》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:42.226273"}}, "37fe661e9b7b41e8": {"lesson_id": "ba2488342e3742a0ada23253436f807c", "template_type": "complex", "file_path": "cache/complex_prompts/ba2488342e3742a0ada23253436f807c_complex_prompt.txt", "cached_at": "2025-07-25T00:54:44.328529", "prompt_length": 865, "metadata": {"lesson_name": "轴对称（其它）", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:44.309010"}}, "75a13a12a96ca2b1": {"lesson_id": "6b0afb5bd4e34194bcfffc8a7aa7f705", "template_type": "complex", "file_path": "cache/complex_prompts/6b0afb5bd4e34194bcfffc8a7aa7f705_complex_prompt.txt", "cached_at": "2025-07-25T00:54:46.383984", "prompt_length": 934, "metadata": {"lesson_name": "6.陶罐和铁罐", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:46.383801"}}, "e601763415900bfb": {"lesson_id": "ecdc7169e7fd442fb6d6f9dc0afeb791", "template_type": "complex", "file_path": "cache/complex_prompts/ecdc7169e7fd442fb6d6f9dc0afeb791_complex_prompt.txt", "cached_at": "2025-07-25T00:54:48.424868", "prompt_length": 961, "metadata": {"lesson_name": "三角形的面积", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:48.424690"}}, "7a052dd225080dcb": {"lesson_id": "b784b7f8db954abdb45db3af88afbd00", "template_type": "complex", "file_path": "cache/complex_prompts/b784b7f8db954abdb45db3af88afbd00_complex_prompt.txt", "cached_at": "2025-07-25T00:54:50.645700", "prompt_length": 983, "metadata": {"lesson_name": "Unit 1 It allows people to get closer to them.", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:50.645536"}}, "aec5fe2b4eb9701b": {"lesson_id": "34ed65b9da1e4b7996b7ba54ac9a1a02", "template_type": "complex", "file_path": "cache/complex_prompts/34ed65b9da1e4b7996b7ba54ac9a1a02_complex_prompt.txt", "cached_at": "2025-07-25T00:54:52.776850", "prompt_length": 1003, "metadata": {"lesson_name": "酵母细胞的固定化", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:52.776694"}}, "a261561dfe30befd": {"lesson_id": "362c6560998d4473adabef9299d493be", "template_type": "complex", "file_path": "cache/complex_prompts/362c6560998d4473adabef9299d493be_complex_prompt.txt", "cached_at": "2025-07-25T00:54:54.824519", "prompt_length": 931, "metadata": {"lesson_name": "四个太阳", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:54.824351"}}, "68963708d9bd792b": {"lesson_id": "32a030a1432646749faa40c0b9b4af59", "template_type": "complex", "file_path": "cache/complex_prompts/32a030a1432646749faa40c0b9b4af59_complex_prompt.txt", "cached_at": "2025-07-25T00:54:56.896728", "prompt_length": 1001, "metadata": {"lesson_name": "《彝家娃娃真幸福》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:56.896574"}}, "4f707fb3bc5e9156": {"lesson_id": "6179be0c5cf845f199d7839d49323b74", "template_type": "complex", "file_path": "cache/complex_prompts/6179be0c5cf845f199d7839d49323b74_complex_prompt.txt", "cached_at": "2025-07-25T00:54:59.122784", "prompt_length": 836, "metadata": {"lesson_name": "纸的发明", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:54:59.122622"}}, "d53fba8253325720": {"lesson_id": "fcc4c0fe296c44c28387bd21efad827e", "template_type": "complex", "file_path": "cache/complex_prompts/fcc4c0fe296c44c28387bd21efad827e_complex_prompt.txt", "cached_at": "2025-07-25T00:55:01.486594", "prompt_length": 837, "metadata": {"lesson_name": "因数和倍数", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:55:01.486427"}}, "0459d50ccc037ea6": {"lesson_id": "8864fe089ea24387a8050ceb24f9b241", "template_type": "complex", "file_path": "cache/complex_prompts/8864fe089ea24387a8050ceb24f9b241_complex_prompt.txt", "cached_at": "2025-07-25T00:55:03.713079", "prompt_length": 879, "metadata": {"lesson_name": "不一样的你我他", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:55:03.712915"}}, "e2813dbb7fb6a672": {"lesson_id": "59adcae81b4d4287a2a27b9d023b9588", "template_type": "complex", "file_path": "cache/complex_prompts/59adcae81b4d4287a2a27b9d023b9588_complex_prompt.txt", "cached_at": "2025-07-25T00:55:05.914015", "prompt_length": 958, "metadata": {"lesson_name": "遨游汉字王国", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:55:05.913836"}}, "9f592232a3829bed": {"lesson_id": "1257e4381801442288fe0161633f0670", "template_type": "complex", "file_path": "cache/complex_prompts/1257e4381801442288fe0161633f0670_complex_prompt.txt", "cached_at": "2025-07-25T00:55:08.102739", "prompt_length": 943, "metadata": {"lesson_name": "我能独立学习", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:55:08.102562"}}, "4ab4d2a60a1eb819": {"lesson_id": "c0cd4e2ebfd0416995a03484fd982ab4", "template_type": "complex", "file_path": "cache/complex_prompts/c0cd4e2ebfd0416995a03484fd982ab4_complex_prompt.txt", "cached_at": "2025-07-25T00:55:10.176109", "prompt_length": 903, "metadata": {"lesson_name": "探寻新航路", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:55:10.175944"}}, "309f29ffb85ee2ef": {"lesson_id": "68187738caba4d46a8c63cc67b1b1f0e", "template_type": "complex", "file_path": "cache/complex_prompts/68187738caba4d46a8c63cc67b1b1f0e_complex_prompt.txt", "cached_at": "2025-07-25T00:55:12.292094", "prompt_length": 1008, "metadata": {"lesson_name": "第七单元 课题1 燃烧和灭火", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:55:12.291935"}}, "54e174818fb08ba4": {"lesson_id": "64d8daaef52f459ab576b0db1c5a443c", "template_type": "complex", "file_path": "cache/complex_prompts/64d8daaef52f459ab576b0db1c5a443c_complex_prompt.txt", "cached_at": "2025-07-25T00:55:14.616296", "prompt_length": 879, "metadata": {"lesson_name": "邹忌讽齐王纳谏", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:55:14.616118"}}, "d82a351582b812e7": {"lesson_id": "2b884505fadd47f18be931a2a8c008db", "template_type": "complex", "file_path": "cache/complex_prompts/2b884505fadd47f18be931a2a8c008db_complex_prompt.txt", "cached_at": "2025-07-25T00:55:16.658724", "prompt_length": 963, "metadata": {"lesson_name": "《21 文言文二则》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:55:16.658546"}}, "97f5a76b28738f9b": {"lesson_id": "3589d5f19b7d4e2aa3ba8b5b391653dc", "template_type": "complex", "file_path": "cache/complex_prompts/3589d5f19b7d4e2aa3ba8b5b391653dc_complex_prompt.txt", "cached_at": "2025-07-25T00:55:18.686025", "prompt_length": 933, "metadata": {"lesson_name": "《植树问题》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:55:18.685844"}}, "8155919aac4f29d0": {"lesson_id": "5a535dcd4aa14df5a8a6f6553b631d66", "template_type": "complex", "file_path": "cache/complex_prompts/5a535dcd4aa14df5a8a6f6553b631d66_complex_prompt.txt", "cached_at": "2025-07-25T00:55:20.734996", "prompt_length": 864, "metadata": {"lesson_name": "有余数的除法", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:55:20.734811"}}, "309383b681194c54": {"lesson_id": "26ccabbfd13c4a0a93278e2df43e81d2", "template_type": "complex", "file_path": "cache/complex_prompts/26ccabbfd13c4a0a93278e2df43e81d2_complex_prompt.txt", "cached_at": "2025-07-25T00:55:22.758871", "prompt_length": 876, "metadata": {"lesson_name": "守株待兔", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:55:22.758694"}}, "dcf8aedeef5ceef4": {"lesson_id": "9bf18f740c6a499f8ef79848286f774f", "template_type": "complex", "file_path": "cache/complex_prompts/9bf18f740c6a499f8ef79848286f774f_complex_prompt.txt", "cached_at": "2025-07-25T00:55:24.778884", "prompt_length": 947, "metadata": {"lesson_name": "直角三角形全等的判定", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:55:24.778705"}}, "0cacbb46a29c9a39": {"lesson_id": "c988d0681b964643b2b929e1eb6b4f27", "template_type": "complex", "file_path": "cache/complex_prompts/c988d0681b964643b2b929e1eb6b4f27_complex_prompt.txt", "cached_at": "2025-07-25T00:55:26.868221", "prompt_length": 899, "metadata": {"lesson_name": "《Unit 10 You're supposed to shake hands.》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:55:26.868044"}}, "5641cf50b577383e": {"lesson_id": "7e3c8726401f452cbaa430400ac336a1", "template_type": "complex", "file_path": "cache/complex_prompts/7e3c8726401f452cbaa430400ac336a1_complex_prompt.txt", "cached_at": "2025-07-25T00:55:28.949959", "prompt_length": 860, "metadata": {"lesson_name": "琥珀", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:55:28.949786"}}, "416135c5cd925980": {"lesson_id": "06e28825bee94586bcdd4ffaaa53db6c", "template_type": "complex", "file_path": "cache/complex_prompts/06e28825bee94586bcdd4ffaaa53db6c_complex_prompt.txt", "cached_at": "2025-07-25T00:55:31.066273", "prompt_length": 877, "metadata": {"lesson_name": "《哺乳类》", "subject": "未知学科", "grade_level": "未知学段", "teacher": "未知教师", "is_template_only": true, "cached_at": "2025-07-25T00:55:31.066119"}}}, "statistics": {"total_cached": 617, "cache_hits": 5853, "cache_misses": 1920}}